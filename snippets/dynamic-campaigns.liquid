{% comment %}
  Renders dynamic campaigns in cart
  
  Usage:
  {% render 'dynamic-campaigns' %}
{% endcomment %}

{%- if settings.dynamic_campaigns_enabled -%}
  {{ 'component-dynamic-campaigns.css' | asset_url | stylesheet_tag }}
  
  <div class="dynamic-campaigns-wrapper">
    {%- if settings.campaigns_heading != blank -%}
      <h2 class="campaigns-main-title">{{ settings.campaigns_heading }}</h2>
    {%- endif -%}
    
    <div class="dynamic-campaigns-container">
      <div class="campaigns-loading">
        <div class="campaigns-loading-spinner"></div>
        <span>Kampanyalar yükleniyor...</span>
      </div>
    </div>
  </div>

  <script>
    // Pass theme settings to JavaScript
    window.themeSettings = window.themeSettings || {};
    window.themeSettings.dynamic_campaigns_enabled = {{ settings.dynamic_campaigns_enabled | json }};
    window.themeSettings.show_campaign_progress = {{ settings.show_campaign_progress | json }};
    window.themeSettings.show_campaign_suggestions = {{ settings.show_campaign_suggestions | json }};
    window.themeSettings.campaigns_heading = {{ settings.campaigns_heading | json }};
    window.themeSettings.cart_campaigns = {{ settings.cart_campaigns | json }};
    window.themeSettings.product_campaigns = {{ settings.product_campaigns | json }};

    console.log('Dynamic campaigns snippet loaded');
    console.log('Settings:', window.themeSettings);
  </script>

  <script src="{{ 'dynamic-campaigns.js' | asset_url }}" defer="defer"></script>
{%- endif -%}
