{{ 'section-vorna-testimonials.css' | asset_url | stylesheet_tag }}

<div class="vorna-testimonials color-{{ section.settings.color_scheme }} gradient">
  <div class="page-width">
    {%- if section.settings.title != blank -%}
      <div class="vorna-testimonials__header">
        <h2 class="vorna-testimonials__title">{{ section.settings.title }}</h2>
      </div>
    {%- endif -%}
    
    <div class="vorna-testimonials__grid">
      {%- for block in section.blocks -%}
        <div class="vorna-testimonials__item" {{ block.shopify_attributes }}>
          <div class="vorna-testimonials__content">
            {%- if block.settings.quote != blank -%}
              <blockquote class="vorna-testimonials__quote">
                "{{ block.settings.quote }}"
              </blockquote>
            {%- endif -%}
            
            {%- if block.settings.author != blank -%}
              <cite class="vorna-testimonials__author">
                {{ block.settings.author }}
              </cite>
            {%- endif -%}
          </div>
        </div>
      {%- endfor -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "VORNA Testimonials",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Müşterilerimizden yorumlar",
      "label": "Title"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 60
    }
  ],
  "blocks": [
    {
      "type": "testimonial",
      "name": "Testimonial",
      "settings": [
        {
          "type": "textarea",
          "id": "quote",
          "default": "Spor salonunda eşyalarımı artık yere atmıyorum. Mıknatısı çok güçlü, her şey düzenli duruyor. Çok memnun kaldım.",
          "label": "Quote"
        },
        {
          "type": "text",
          "id": "author",
          "default": "Emre Yıldırım",
          "label": "Author"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "VORNA Testimonials",
      "blocks": [
        {
          "type": "testimonial"
        },
        {
          "type": "testimonial"
        },
        {
          "type": "testimonial"
        }
      ]
    }
  ]
}
{% endschema %}
