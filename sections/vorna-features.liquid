{{ 'section-vorna-features.css' | asset_url | stylesheet_tag }}

<div class="vorna-features color-{{ section.settings.color_scheme }} gradient">
  <div class="page-width">
    {%- if section.settings.title != blank -%}
      <div class="vorna-features__header">
        <h2 class="vorna-features__title">{{ section.settings.title }}</h2>
      </div>
    {%- endif -%}
    
    <div class="vorna-features__grid">
      {%- for block in section.blocks -%}
        <div class="vorna-features__item" {{ block.shopify_attributes }}>
          {%- if block.settings.image != blank -%}
            <div class="vorna-features__image">
              <img
                src="{{ block.settings.image | image_url: width: 400 }}"
                alt="{{ block.settings.image.alt | escape }}"
                loading="lazy"
                width="400"
                height="{{ 400 | divided_by: block.settings.image.aspect_ratio | ceil }}"
              >
            </div>
          {%- endif -%}
          
          <div class="vorna-features__content">
            {%- if block.settings.heading != blank -%}
              <h3 class="vorna-features__heading">{{ block.settings.heading }}</h3>
            {%- endif -%}
            
            {%- if block.settings.text != blank -%}
              <div class="vorna-features__text">
                {{ block.settings.text }}
              </div>
            {%- endif -%}
          </div>
        </div>
      {%- endfor -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "VORNA Features",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Manyetik spor çantası neden önemlidir",
      "label": "Title"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 60
    }
  ],
  "blocks": [
    {
      "type": "feature",
      "name": "Feature",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "Güçlü manyetik tutuş",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Arkasındaki mıknatıs sayesinde eşyalarınızı düzenli ve kolayca istediğiniz yere sabitleyebilirsiniz.</p>",
          "label": "Description"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "VORNA Features",
      "blocks": [
        {
          "type": "feature"
        },
        {
          "type": "feature"
        },
        {
          "type": "feature"
        }
      ]
    }
  ]
}
{% endschema %}
