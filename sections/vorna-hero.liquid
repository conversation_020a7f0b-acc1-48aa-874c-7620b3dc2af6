{{ 'section-vorna-hero.css' | asset_url | stylesheet_tag }}

<div class="vorna-hero color-{{ section.settings.color_scheme }} gradient">
  <div class="page-width">
    <div class="vorna-hero__content">
      {%- if section.settings.heading != blank -%}
        <h1 class="vorna-hero__heading inline-richtext {{ section.settings.heading_size }}">
          {{ section.settings.heading }}
        </h1>
      {%- endif -%}
      
      {%- if section.settings.subheading != blank -%}
        <div class="vorna-hero__subheading">
          {{ section.settings.subheading }}
        </div>
      {%- endif -%}
      
      {%- if section.settings.button_label != blank -%}
        <div class="vorna-hero__buttons">
          <a
            {% if section.settings.button_link == blank %}
              role="link" aria-disabled="true"
            {% else %}
              href="{{ section.settings.button_link }}"
            {% endif %}
            class="button button--primary button--large"
          >
            {{ section.settings.button_label | escape }}
          </a>
        </div>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "VORNA Hero",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "Manyetik çantamızla sporda daha rahat ve düzenli ol.",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "richtext",
      "id": "subheading",
      "default": "<p>Vorna artık son verecek!</p>",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Şimdi Sipariş Ver",
      "label": "Button label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 80
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 80
    }
  ],
  "presets": [
    {
      "name": "VORNA Hero"
    }
  ]
}
{% endschema %}
