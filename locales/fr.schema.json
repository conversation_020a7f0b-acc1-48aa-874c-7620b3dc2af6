{"settings_schema": {"colors": {"name": "Couleurs", "settings": {"background": {"label": "Arrière‑plan"}, "background_gradient": {"label": "Dégradé de l’arrière-plan", "info": "Le dégradé de l’arrière-plan remplace l’arrière-plan là où c’est possible."}, "text": {"label": "Texte"}, "button_background": {"label": "Arrière-plan plein du bouton"}, "button_label": {"label": "Texte de bouton plein"}, "secondary_button_label": {"label": "Bouton en relief"}, "shadow": {"label": "Ombre"}}}, "typography": {"name": "Typographie", "settings": {"type_header_font": {"label": "Police"}, "header__1": {"content": "Titres"}, "header__2": {"content": "Corps"}, "type_body_font": {"label": "Police"}, "heading_scale": {"label": "<PERSON><PERSON><PERSON>"}, "body_scale": {"label": "<PERSON><PERSON><PERSON>"}}}, "social-media": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"social_twitter_link": {"label": "X / Twitter", "info": "https://x.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://vimeo.com/shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Co<PERSON><PERSON> sociaux"}}}, "currency_format": {"name": "Format de devise", "settings": {"currency_code_enabled": {"label": "Codes de devises"}, "paragraph": "Les prix affichés dans le panier et sur la page de paiement indiquent toujours les codes de devises"}}, "layout": {"name": "Mise en page", "settings": {"page_width": {"label": "<PERSON><PERSON> de la page"}, "spacing_sections": {"label": "Espace entre les sections du modèle"}, "header__grid": {"content": "Grille"}, "paragraph__grid": {"content": "Affecte les zones présentant plusieurs colonnes ou lignes"}, "spacing_grid_horizontal": {"label": "Espace horizontal"}, "spacing_grid_vertical": {"label": "Espace vertical"}}}, "search_input": {"name": "Comportement de recherche", "settings": {"predictive_search_enabled": {"label": "Suggestions de recherche"}, "predictive_search_show_vendor": {"label": "Fournisseur de produit", "info": "Affiché lorsque les suggestions de recherche sont activées"}, "predictive_search_show_price": {"label": "Prix du produit", "info": "Affiché lorsque les suggestions de recherche sont activées"}}}, "global": {"settings": {"header__border": {"content": "Bordure"}, "header__shadow": {"content": "Ombre"}, "blur": {"label": "<PERSON><PERSON>"}, "corner_radius": {"label": "Rayon de coin"}, "horizontal_offset": {"label": "Décalage horizontal"}, "vertical_offset": {"label": "Décalage vertical"}, "thickness": {"label": "Épaisseur"}, "opacity": {"label": "Opacité"}, "image_padding": {"label": "Marge intérieure de l'image"}, "text_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du texte"}}}, "badges": {"name": "Badges", "settings": {"position": {"options__1": {"label": "En bas à gauche"}, "options__2": {"label": "En bas à droite"}, "options__3": {"label": "En haut à gauche"}, "options__4": {"label": "En haut à droite"}, "label": "Position sur les cartes"}, "sale_badge_color_scheme": {"label": "Nuancier de couleurs du badge de vente"}, "sold_out_badge_color_scheme": {"label": "Nuancier de couleurs du badge de rupture de stock"}}}, "buttons": {"name": "Boutons"}, "variant_pills": {"name": "Boutons pilule de variante", "paragraph": "Les boutons pilule de variante sont un moyen d’afficher vos [variantes de produit](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block)"}, "inputs": {"name": "Entrées"}, "content_containers": {"name": "Conteneurs de contenu"}, "popups": {"name": "Menus dé<PERSON>lants et pop-ups", "paragraph": "Affecte les zones telles que les menus déroulants de navigation, les fenêtres modales pop-up et les fenêtres pop-up de panier"}, "media": {"name": "Support multimédia"}, "drawers": {"name": "<PERSON><PERSON><PERSON>"}, "cart": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "Type", "drawer": {"label": "Tiroir"}, "page": {"label": "Page"}, "notification": {"label": "Notification contextuelle"}}, "show_vendor": {"label": "Fournisseur"}, "show_cart_note": {"label": "Note de panier"}, "cart_drawer": {"header": "<PERSON><PERSON>", "collection": {"label": "Collection", "info": "Affiché lorsque le panier coulissant est vide"}}}}, "cards": {"name": "Cartes de produit", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Style"}}}, "collection_cards": {"name": "Cartes de collection", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Style"}}}, "blog_cards": {"name": "Cartes de blog", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Style"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "<PERSON><PERSON>"}, "favicon": {"label": "Favicon", "info": "Affiché à 32 x 32 px"}}}, "brand_information": {"name": "Informations sur la marque", "settings": {"brand_headline": {"label": "Titre"}, "brand_description": {"label": "Description"}, "brand_image": {"label": "Image"}, "brand_image_width": {"label": "Largeur d’image"}, "paragraph": {"content": "S’affiche dans le bloc d’informations de marque du pied de page"}}}, "animations": {"name": "Animations", "settings": {"animations_reveal_on_scroll": {"label": "Afficher les sections lors du défilement"}, "animations_hover_elements": {"options__1": {"label": "Aucun"}, "options__2": {"label": "Levée verticale"}, "label": "<PERSON><PERSON><PERSON> de <PERSON>vol", "info": "Affecte les cartes et les boutons", "options__3": {"label": "3D Levée"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Remplissage", "padding_top": "<PERSON><PERSON>", "padding_bottom": "Bas"}, "spacing": "Espacement", "colors": {"label": "Nuancier de couleurs", "has_cards_info": "Pour modifier le nuancier de couleur de la carte, mettez à jour les paramètres de votre thème."}, "heading_size": {"label": "<PERSON><PERSON> du titre", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "options__4": {"label": "Très grand"}, "options__5": {"label": "Très très grand"}}, "image_shape": {"options__1": {"label": "Forme par défaut"}, "options__2": {"label": "<PERSON>e"}, "options__3": {"label": "Indistincte"}, "options__4": {"label": "Chevron gauche"}, "options__5": {"label": "Chevron droite"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Parallélogramme"}, "options__8": {"label": "Cercle"}, "label": "Forme d’image"}, "animation": {"content": "Animations", "image_behavior": {"options__1": {"label": "Aucun"}, "options__2": {"label": "Mouvement ambiant"}, "label": "Animations", "options__3": {"label": "Position de l’arrière-plan fixe"}, "options__4": {"label": "Zoom sur le défilement"}}}}, "announcement-bar": {"name": "Barre d'annonces", "blocks": {"announcement": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "Texte", "default": "Bienvenue dans notre boutique"}, "text_alignment": {"label": "Alignement du texte", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "link": {"label": "<PERSON><PERSON>"}}}}, "settings": {"auto_rotate": {"label": "Rotation automatique des annonces"}, "change_slides_speed": {"label": "Changer toutes les"}, "show_social": {"label": "Icônes de médias sociaux", "info": "[<PERSON><PERSON><PERSON> les comptes sociaux](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "Sélecteur de pays/région", "info": "[Gérer les pays/régions](/admin/settings/markets)"}, "enable_language_selector": {"label": "<PERSON><PERSON><PERSON><PERSON> de langue", "info": "[<PERSON><PERSON><PERSON> les langues](/admin/settings/languages)"}, "heading_utilities": {"content": "Utilitaires"}, "paragraph": {"content": "Apparaît uniquement sur les grands écrans"}}, "presets": {"name": "Barre d’annonces"}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "Titre", "default": "Collage multimédia"}, "desktop_layout": {"label": "Mise en page", "options__1": {"label": "Grand bloc en premier"}, "options__2": {"label": "Grand bloc en dernier"}}, "mobile_layout": {"label": "Mise en page pour téléphone portable", "options__1": {"label": "Collage"}, "options__2": {"label": "Colonne"}}, "card_styles": {"label": "Style de cartes", "info": "Gérer les styles de cartes individuels dans [paramètres du thème](/editor?context=theme&category=product%20cards)", "options__1": {"label": "Utiliser des styles de cartes individuels"}, "options__2": {"label": "Style pour toutes en tant que cartes de produits"}}, "header_layout": {"content": "Mise en page"}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}}}, "product": {"name": "Produit", "settings": {"product": {"label": "Produit"}, "secondary_background": {"label": "Afficher l'arrière-plan secondaire"}, "second_image": {"label": "Afficher la deuxième image en survol"}}}, "collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}, "video": {"name": "Vidéo", "settings": {"cover_image": {"label": "Image de couverture"}, "video_url": {"label": "URL", "info": "Si la section contient d'autres blocs, la vidéo est lue dans une fenêtre pop-up.", "placeholder": "Utiliser une URL YouTube ou Vimeo"}, "description": {"label": "Texte alternatif de la vidéo", "info": "Décrivez la vidéo pour les clients utilisant des lecteurs d'écran. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "default": "Description de la vidéo"}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Liste des collections", "settings": {"title": {"label": "Titre", "default": "Collections"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "swipe_on_mobile": {"label": "Carrousel"}, "show_view_all": {"label": "Bouton « Tout afficher »", "info": "Visible si la liste comprend plus de collections que celles affichées"}, "columns_desktop": {"label": "Colonnes"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Colonnes", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_layout": {"content": "Mise en page"}}, "blocks": {"featured_collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}}, "presets": {"name": "Liste des collections"}}, "contact-form": {"name": "Formulaire de contact", "presets": {"name": "Formulaire de contact"}, "settings": {"title": {"default": "Formulaire de contact", "label": "Titre"}}}, "custom-liquid": {"name": "Liquid personnalisé", "settings": {"custom_liquid": {"label": "Code Liquid", "info": "Ajoutez des extraits d’application ou autre code pour créer des personnalisations avancées. [En savoir plus](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Liquid personnalisé"}}, "featured-blog": {"name": "Articles de blog", "settings": {"heading": {"label": "Titre", "default": "Articles de blog"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Nombre d’articles"}, "show_view_all": {"label": "Bouton « Tout afficher »", "info": "Visible si le blog comprend plus d’articles que ceux affichés"}, "show_image": {"label": "Image vedette"}, "show_date": {"label": "Date"}, "show_author": {"label": "<PERSON><PERSON><PERSON>"}, "columns_desktop": {"label": "Colonnes"}, "layout_header": {"content": "Mise en page"}, "text_header": {"content": "Texte"}}, "presets": {"name": "Articles de blog"}}, "featured-collection": {"name": "Collection en vedette", "settings": {"title": {"label": "Titre", "default": "Collection en vedette"}, "collection": {"label": "Collection"}, "products_to_show": {"label": "Nombre de produits"}, "show_view_all": {"label": "Bouton « Tout afficher »", "info": "Visible si la collection comprend plus de produits que ceux affichés"}, "header": {"content": "Carte de produit"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "show_vendor": {"label": "Fournisseur"}, "show_rating": {"label": "Évaluation de produit", "info": "Une application est requise pour les évaluations. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "A<PERSON>t rapide"}, "columns_desktop": {"label": "Colonnes"}, "description": {"label": "Description"}, "show_description": {"label": "Afficher la description de la collection à partir de l’interface administrateur"}, "description_style": {"label": "Style de la description", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "view_all_style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Bouton en relief"}, "options__3": {"label": "Bouton plein"}, "label": "Style « Tout afficher »"}, "enable_desktop_slider": {"label": "Carrousel"}, "full_width": {"label": "Produits pleine largeur"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Colonnes", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "swipe_on_mobile": {"label": "Carrousel"}, "header_text": {"content": "Texte"}, "header_collection": {"content": "Mise en page de la collection"}}, "presets": {"name": "Collection en vedette"}}, "footer": {"name": "Pied de page", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON>-tête", "default": "Liens rapides"}, "menu": {"label": "<PERSON><PERSON>"}}}, "text": {"name": "Texte", "settings": {"heading": {"label": "<PERSON>-tête", "default": "Titre"}, "subtext": {"label": "Sous-texte", "default": "<p>Partagez les coordonnées, les détails de la boutique, les promotions ou le contenu de la marque avec vos clients.</p>"}}}, "brand_information": {"name": "Informations sur la marque", "settings": {"paragraph": {"content": "Gérer les informations de marque dans [paramètres du thème](/editor?context=theme&category=brand%20information)"}, "show_social": {"label": "Icônes de médias sociaux", "info": "[<PERSON><PERSON><PERSON> les comptes sociaux](/editor?context=theme&category=social%20media)"}}}}, "settings": {"newsletter_enable": {"label": "Inscription à la liste de diffusion"}, "newsletter_heading": {"label": "<PERSON>-tête", "default": "S’abonner à nos e-mails"}, "header__1": {"content": "Inscription à la liste de diffusion", "info": "Ajout d’inscriptions [profils clients](https://help.shopify.com/manual/customers/manage-customers)"}, "show_social": {"label": "Icônes de médias sociaux", "info": "[<PERSON><PERSON><PERSON> les comptes sociaux](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "Sélecteur de pays/région", "info": "[Gérer les pays/régions](/admin/settings/markets)"}, "enable_language_selector": {"label": "<PERSON><PERSON><PERSON><PERSON> de langue", "info": "[<PERSON><PERSON><PERSON> les langues](/admin/settings/languages)"}, "payment_enable": {"label": "Icônes de moyens de paiement"}, "margin_top": {"label": "Marge supérieure"}, "show_policy": {"label": "Liens des politiques", "info": "[<PERSON><PERSON><PERSON> les politiques](/admin/settings/legal)"}, "header__9": {"content": "Utilitaires"}, "enable_follow_on_shop": {"label": "Suivre sur Shop", "info": "Shop Pay doit être activé. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}}}, "header": {"name": "<PERSON>-tête", "settings": {"logo_position": {"label": "Position du logo", "options__1": {"label": "Centré à gauche"}, "options__2": {"label": "En haut à gauche"}, "options__3": {"label": "En haut au centre"}, "options__4": {"label": "Centré au milieu"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Ligne de séparation"}, "margin_bottom": {"label": "Marge inférieure"}, "menu_type_desktop": {"label": "Type de menu", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Méga menu"}, "options__3": {"label": "Tiroir"}}, "mobile_logo_position": {"label": "Position du logo sur mobile", "options__1": {"label": "Centre"}, "options__2": {"label": "G<PERSON><PERSON>"}}, "logo_help": {"content": "Modifier votre logo dans [paramètres du thème](/editor?context=theme&category=logo)"}, "sticky_header_type": {"label": "En‑tête fixe", "options__1": {"label": "Aucun"}, "options__2": {"label": "Lors du défilement vers le haut"}, "options__3": {"label": "Toujours"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>z toujours la taille du logo"}}, "enable_country_selector": {"label": "Sélecteur de pays/région", "info": "[Gérer les pays/régions](/admin/settings/markets)"}, "enable_language_selector": {"label": "<PERSON><PERSON><PERSON><PERSON> de langue", "info": "[<PERSON><PERSON><PERSON> les langues](/admin/settings/languages)"}, "header__1": {"content": "<PERSON><PERSON><PERSON>"}, "menu_color_scheme": {"label": "Nuancier de couleurs de menu"}, "enable_customer_avatar": {"label": "Avatar de compte client", "info": "Visible uniquement lorsque les clients sont connectés avec Shop. [Gérer les comptes clients](/admin/settings/customer_accounts)"}, "header__utilities": {"content": "Utilitaires"}}}, "image-banner": {"name": "Bannière avec image", "settings": {"image": {"label": "Image 1"}, "image_2": {"label": "Image 2"}, "stack_images_on_mobile": {"label": "Empiler les images"}, "show_text_box": {"label": "Conteneur"}, "image_overlay_opacity": {"label": "Opacité de la superposition"}, "show_text_below": {"label": "Conteneur"}, "image_height": {"label": "<PERSON><PERSON>", "options__1": {"label": "Adapter à la première image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}}, "desktop_content_position": {"options__1": {"label": "En haut à gauche"}, "options__2": {"label": "En haut au centre"}, "options__3": {"label": "En haut à droite"}, "options__4": {"label": "Au milieu à gauche"}, "options__5": {"label": "Centré au milieu"}, "options__6": {"label": "Au milieu à droite"}, "options__7": {"label": "En bas à gauche"}, "options__8": {"label": "En bas au centre"}, "options__9": {"label": "En bas à droite"}, "label": "Position"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement"}, "mobile": {"content": "Mise en page sur mobile"}, "content": {"content": "Contenu"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre", "default": "Bannière avec image"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Texte", "default": "Fournissez des détails à votre clientèle sur l’image/les images ou le contenu de la bannière du modèle."}, "text_style": {"options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Style"}}}, "buttons": {"name": "Boutons", "settings": {"button_label_1": {"label": "Étiquette", "info": "Laisser vide pour masquer", "default": "Texte du bouton"}, "button_link_1": {"label": "<PERSON><PERSON>"}, "button_style_secondary_1": {"label": "Style de contour"}, "button_label_2": {"label": "Étiquette", "info": "Laisser vide pour masquer", "default": "Texte du bouton"}, "button_link_2": {"label": "<PERSON><PERSON>"}, "button_style_secondary_2": {"label": "Style de contour"}, "header_1": {"content": "Bouton 1"}, "header_2": {"content": "Bouton 2"}}}}, "presets": {"name": "Bannière avec image"}}, "image-with-text": {"name": "Image avec texte", "settings": {"image": {"label": "Image"}, "height": {"options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON>", "options__4": {"label": "Grand"}}, "layout": {"options__1": {"label": "L'image en premier"}, "options__2": {"label": "Deuxième image"}, "label": "Placement"}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "label": "<PERSON><PERSON>"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Milieu"}, "options__3": {"label": "Bas"}, "label": "Position"}, "content_layout": {"options__1": {"label": "Aucun chevauchement"}, "options__2": {"label": "Chevauchement"}, "label": "Mise en page"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement sur mobile"}, "header": {"content": "Contenu"}, "header_colors": {"content": "Couleurs"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre", "default": "Image avec texte"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Texte", "default": "<p>Associez un texte à une image pour mettre en avant le produit, la collection ou l’article de blog de votre choix. Ajoutez des informations sur la disponibilité ou le style. Vous pouvez même fournir un avis.</p>"}, "text_style": {"label": "Style", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}}}}, "button": {"name": "Bouton", "settings": {"button_label": {"label": "Étiquette", "info": "Laisser vide pour masquer", "default": "Texte du bouton"}, "button_link": {"label": "<PERSON><PERSON>"}, "outline_button": {"label": "Style de contour"}}}, "caption": {"name": "Légende", "settings": {"text": {"label": "Texte", "default": "Ajouter un slogan"}, "text_style": {"label": "Style", "options__1": {"label": "Sous-titre"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}}}}, "presets": {"name": "Image avec texte"}}, "main-article": {"name": "Article de blog", "blocks": {"featured_image": {"name": "Image vedette", "settings": {"image_height": {"label": "Hauteur de l’image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}}}}, "title": {"name": "Titre", "settings": {"blog_show_date": {"label": "Date"}, "blog_show_author": {"label": "<PERSON><PERSON><PERSON>"}}}, "content": {"name": "Contenu"}, "share": {"name": "Partager", "settings": {"text": {"label": "Texte", "default": "Partager"}}}}}, "main-blog": {"name": "Articles de blog", "settings": {"show_image": {"label": "Image vedette"}, "show_date": {"label": "Date"}, "show_author": {"label": "<PERSON><PERSON><PERSON>"}, "layout": {"label": "Mise en page", "options__1": {"label": "Grille"}, "options__2": {"label": "Collage"}}, "image_height": {"label": "Hauteur de l’image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}}}}, "main-cart-footer": {"name": "Sous-total", "blocks": {"subtotal": {"name": "Sous-total du prix"}, "buttons": {"name": "Bouton de paiement"}}}, "main-cart-items": {"name": "Articles"}, "main-collection-banner": {"name": "Bannière de collection", "settings": {"paragraph": {"content": "Les détails de la collection details sont [gérés dans votre interface administrateur](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Description"}, "show_collection_image": {"label": "Image"}}}, "main-collection-product-grid": {"name": "Grille de produit", "settings": {"products_per_page": {"label": "Produits par page"}, "enable_filtering": {"label": "Filtres", "info": "Personnaliser les filtres avec l’[application Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "<PERSON><PERSON>"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "show_vendor": {"label": "Fournisseur"}, "header__1": {"content": "Filtrage et tri"}, "header__3": {"content": "Carte de produit"}, "enable_tags": {"label": "Filtres", "info": "Personnaliser les filtres avec l’[application Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "show_rating": {"label": "Évaluation de produit", "info": "Une application est requise pour les évaluations de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"}, "columns_desktop": {"label": "Colonnes"}, "columns_mobile": {"label": "Colonnes sur mobile", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "filter_type": {"label": "Mise en page du filtre", "options__1": {"label": "Horizontale"}, "options__2": {"label": "Verticale"}, "options__3": {"label": "Tiroir"}}, "quick_add": {"label": "A<PERSON>t rapide", "options": {"option_1": "Aucun", "option_2": "Standard", "option_3": "En gros"}}}}, "main-list-collections": {"name": "Page de liste des collections", "settings": {"title": {"label": "Titre", "default": "Collections"}, "sort": {"label": "Trier les collections", "options__1": {"label": "Alphabétique, de A à Z"}, "options__2": {"label": "Alphabétique, de Z à A"}, "options__3": {"label": "Date, de la plus récente à la plus ancienne"}, "options__4": {"label": "Date, de la plus ancienne à la plus récente"}, "options__5": {"label": "Nombre de produits, par ordre décroissant"}, "options__6": {"label": "Nombre de produits, par ordre croissant"}}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "columns_desktop": {"label": "Colonnes"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Colonnes sur mobile", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "main-page": {"name": "Page"}, "main-password-footer": {"name": "Pied de page du mot de passe"}, "main-password-header": {"name": "En-tête du mot de passe", "settings": {"logo_help": {"content": "Modifier votre logo dans [paramètres du thème](/editor?context=theme&category=logo)"}}}, "main-product": {"blocks": {"text": {"name": "Texte", "settings": {"text": {"label": "Texte", "default": "Bloc de texte"}, "text_style": {"label": "Style", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titre"}, "price": {"name": "Prix"}, "quantity_selector": {"name": "Sélecteur de quantité"}, "variant_picker": {"name": "Sélecteur de variante", "settings": {"picker_type": {"label": "Style", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Comprimés"}}, "swatch_shape": {"label": "Échantillon", "info": "En savoir plus sur [échantillons](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) sur les options de produits", "options__1": {"label": "Cercle"}, "options__2": {"label": "Carré"}, "options__3": {"label": "Aucun"}}}}, "buy_buttons": {"name": "Boutons d'achat", "settings": {"show_dynamic_checkout": {"label": "Boutons de paiement dynamique", "info": "Les clients verront leur option de paiement préférée. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": " Options d’envoi de carte-cadeau", "info": "Les clients peuvent ajouter un message personnel et programmer la date d’envoi. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Disponibilité du service de retrait"}, "description": {"name": "Description"}, "share": {"name": "Partager", "settings": {"text": {"label": "Texte", "default": "Partager"}}}, "collapsible_tab": {"name": "Rangée réductible", "settings": {"heading": {"label": "Titre", "default": "Rangée réductible"}, "content": {"label": "Contenu de la rangée"}, "page": {"label": "Contenu de la rangée de la page"}, "icon": {"options__1": {"label": "Aucune"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Bouteille"}, "options__5": {"label": "<PERSON><PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON>"}, "options__7": {"label": "Bulle de chat"}, "options__8": {"label": "Coche"}, "options__9": {"label": "Presse-papiers"}, "options__10": {"label": "Produits laitiers"}, "options__11": {"label": "Sans produits laitiers"}, "options__12": {"label": "Sèche-linge"}, "options__13": {"label": "Œil"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Sans gluten"}, "options__16": {"label": "<PERSON><PERSON><PERSON>"}, "options__17": {"label": "Fer"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON><PERSON>"}, "options__20": {"label": "Foudre"}, "options__21": {"label": "Rouge à lèvres"}, "options__22": {"label": "Cadenas"}, "options__23": {"label": "Épingle sur la carte"}, "options__24": {"label": "Sans noix"}, "label": "Icône", "options__25": {"label": "Pantalons"}, "options__26": {"label": "Empreinte"}, "options__27": {"label": "Poivre"}, "options__28": {"label": "Parfum"}, "options__29": {"label": "Avion"}, "options__30": {"label": "Plantes"}, "options__31": {"label": "Étiquette de prix"}, "options__32": {"label": "Point d'interrogation"}, "options__33": {"label": "Recyclage"}, "options__34": {"label": "Retour"}, "options__35": {"label": "<PERSON><PERSON><PERSON>"}, "options__36": {"label": "Plat de service"}, "options__37": {"label": "Chemise"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Flocon de neige"}, "options__41": {"label": "<PERSON><PERSON><PERSON>"}, "options__42": {"label": "Chronomètre"}, "options__43": {"label": "Camion"}, "options__44": {"label": "Lavage"}}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Étiquette de lien", "default": "<PERSON><PERSON> texte pop-up"}, "page": {"label": "Page"}}}, "rating": {"name": "Note de produit", "settings": {"paragraph": {"content": "Une application est requise pour les évaluations de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"}}}, "complementary_products": {"name": "Produits complémentaires", "settings": {"paragraph": {"content": "<PERSON><PERSON>rer les produits complémentaires dans l’[application Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "<PERSON>-tête", "default": "S’associe bien avec"}, "make_collapsible_row": {"label": "Rangée réductible"}, "icon": {"info": "Affiché lorsqu’une rangée réductible est sélectionnée"}, "product_list_limit": {"label": "Nombre de produits"}, "products_per_page": {"label": "Produits par page"}, "pagination_style": {"label": "Pagination", "options": {"option_1": "Points", "option_2": "Compteur", "option_3": "<PERSON><PERSON><PERSON><PERSON>"}}, "product_card": {"heading": "Carte de produit"}, "image_ratio": {"label": "Rapport d’aspect de l’image", "options": {"option_1": "Portrait", "option_2": "Carré"}}, "enable_quick_add": {"label": "A<PERSON>t rapide"}}}, "icon_with_text": {"name": "Icône avec texte", "settings": {"layout": {"label": "Mise en page", "options__1": {"label": "Horizontale"}, "options__2": {"label": "Verticale"}}, "heading": {"info": "Laisser vide pour masquer ce jumelage"}, "icon_1": {"label": "Icône"}, "image_1": {"label": "Image"}, "heading_1": {"label": "Titre", "default": "Titre"}, "icon_2": {"label": "Icône"}, "image_2": {"label": "Image"}, "heading_2": {"label": "Titre", "default": "Titre"}, "icon_3": {"label": "Icône"}, "image_3": {"label": "Image"}, "heading_3": {"label": "Titre", "default": "Titre"}, "pairing_1": {"label": "Jumelage 1", "info": "Choisir une icône ou ajouter une image pour chaque jumelage"}, "pairing_2": {"label": "Jumelage 2"}, "pairing_3": {"label": "Jumelage 3"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous‑titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "inventory": {"name": "État des stocks", "settings": {"text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous‑titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Seuil de stock faible"}, "show_inventory_quantity": {"label": "Inventaire"}}}}, "settings": {"header": {"content": "Support multimédia"}, "enable_video_looping": {"label": "Lire la vidéo en boucle"}, "enable_sticky_info": {"label": "Contenu fixe"}, "hide_variants": {"label": "Masquer le support multimédia des autres variantes après en avoir sélectionné une"}, "gallery_layout": {"label": "Mise en page", "options__1": {"label": "Empilé"}, "options__2": {"label": "2 colonnes"}, "options__3": {"label": "Vignettes"}, "options__4": {"label": "Carrousel de vignettes"}}, "media_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}, "mobile_thumbnails": {"label": "Mise en page sur mobile", "options__1": {"label": "2 colonnes"}, "options__2": {"label": "Aff<PERSON><PERSON> les vignettes"}, "options__3": {"label": "Masquer les vignettes"}}, "media_position": {"label": "Position", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_zoom": {"label": "Zoom", "options__1": {"label": "Ouvrir lightbox"}, "options__2": {"label": "Cliquer et passer la souris"}, "options__3": {"label": "Pas de zoom"}}, "constrain_to_viewport": {"label": "Limiter à la hauteur de l’écran"}, "media_fit": {"label": "Ajuster", "options__1": {"label": "Taille d<PERSON>origine"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "name": "Informations produits"}, "main-search": {"name": "Résultats de la recherche", "settings": {"image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "show_vendor": {"label": "Fournisseur"}, "header__1": {"content": "Carte de produit"}, "header__2": {"content": "<PERSON>te de <PERSON>"}, "article_show_date": {"label": "Date"}, "article_show_author": {"label": "<PERSON><PERSON><PERSON>"}, "show_rating": {"label": "Évaluation de produit", "info": "Une application est requise pour les évaluations de produits. [En savoir plus](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"}, "columns_desktop": {"label": "Colonnes"}, "columns_mobile": {"label": "Colonnes sur mobile", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multicolumn": {"name": "Multicolonne", "settings": {"title": {"label": "Titre", "default": "Multicolonne"}, "image_width": {"label": "<PERSON><PERSON>", "options__1": {"label": "Un tiers de largeur de la colonne"}, "options__2": {"label": "Demi-<PERSON>ur de colonne"}, "options__3": {"label": "Largeur complè<PERSON> de colonne"}}, "image_ratio": {"label": "Rapport", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}, "options__4": {"label": "Cercle"}}, "column_alignment": {"label": "Alignement de colonne", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}}, "background_style": {"label": "Arrière-plan secondaire", "options__1": {"label": "Aucune"}, "options__2": {"label": "Afficher comme arrière-plan de la colonne"}}, "button_label": {"label": "Étiquette", "default": "Texte du bouton", "info": "Laisser vide pour masquer"}, "button_link": {"label": "<PERSON><PERSON>"}, "swipe_on_mobile": {"label": "Carrousel"}, "columns_desktop": {"label": "Colonnes"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Colonnes", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_text": {"content": "Titre"}, "header_image": {"content": "Image"}, "header_layout": {"content": "Mise en page"}, "header_button": {"content": "Bouton"}}, "blocks": {"column": {"name": "Colonne", "settings": {"image": {"label": "Image"}, "title": {"label": "Titre", "default": "Colonne"}, "text": {"label": "Description", "default": "<p>Associez un texte à une image pour mettre en avant le produit, la collection ou l’article de blog de votre choix. Ajoutez des informations sur la disponibilité ou le style. Vous pouvez même fournir un avis.</p>"}, "link_label": {"label": "Étiquette de lien", "info": "Laisser vide pour masquer"}, "link": {"label": "<PERSON><PERSON>"}}}}, "presets": {"name": "Multicolonne"}}, "newsletter": {"name": "Inscription à la liste de diffusion", "settings": {"full_width": {"label": "<PERSON><PERSON>e largeur"}, "paragraph": {"content": "Ajout d’inscriptions [profils clients](https://help.shopify.com/manual/customers/manage-customers)"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre", "default": "S’abonner à nos e-mails"}}}, "paragraph": {"name": "Texte", "settings": {"paragraph": {"label": "Texte", "default": "<p>Faites partie des premières personnes à être informées des nouvelles collections et des offres exclusives.</p>"}}}, "email_form": {"name": "Formulaire électronique"}}, "presets": {"name": "Inscription à la liste de diffusion"}}, "page": {"name": "Page", "settings": {"page": {"label": "Page"}}, "presets": {"name": "Page"}}, "rich-text": {"name": "Texte enrichi", "settings": {"full_width": {"label": "<PERSON><PERSON>e largeur"}, "desktop_content_position": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Position du contenu"}, "content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre", "default": "<PERSON><PERSON>r de sa marque"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Texte", "default": "<p>Partagez des informations sur votre marque. Décrivez un produit, partagez des annonces ou souhaitez la bienvenue à vos clients dans votre boutique.</p>"}}}, "buttons": {"name": "Boutons", "settings": {"button_label_1": {"label": "Étiquette", "info": "Laisser vide pour masquer", "default": "Texte du bouton"}, "button_link_1": {"label": "<PERSON><PERSON>"}, "button_style_secondary_1": {"label": "Style de contour"}, "button_label_2": {"label": "Étiquette", "info": "Laisser l’étiquette vide pour masquer"}, "button_link_2": {"label": "<PERSON><PERSON>"}, "button_style_secondary_2": {"label": "Style de contour"}, "header_button1": {"content": "Bouton 1"}, "header_button2": {"content": "Bouton 2"}}}, "caption": {"name": "Légende", "settings": {"text": {"label": "Texte", "default": "Ajouter un slogan"}, "text_style": {"label": "Style", "options__1": {"label": "Sous‑titre"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}}}}, "presets": {"name": "Texte enrichi"}}, "apps": {"name": "Applications", "settings": {"include_margins": {"label": "Rendre les marges des sections identiques à celles du thème"}}, "presets": {"name": "Applications"}}, "video": {"name": "Vidéo", "settings": {"heading": {"label": "<PERSON>-tête", "default": "Vidéo"}, "cover_image": {"label": "Image de couverture"}, "video_url": {"label": "URL", "info": "Utilisez une URL YouTube ou Vimeo"}, "description": {"label": "Texte alternatif de la vidéo", "info": "Décrire la vidéo pour celles et ceux qui utilisent des lecteurs d’écran"}, "image_padding": {"label": "Ajouter une marge intérieure à l'image", "info": "Sélectionnez une marge intérieure pour éviter que votre image de couverture soit rognée."}, "full_width": {"label": "<PERSON><PERSON>e largeur"}, "video": {"label": "Vidéo"}, "enable_video_looping": {"label": "Lire la vidéo en boucle"}, "header__1": {"content": "Vidéo hébergée par Shopify"}, "header__2": {"content": "Ou vidéo intégrée à partir d’une URL"}, "header__3": {"content": "Mise en page"}, "paragraph": {"content": "S’affiche quand aucune vidéo hébergée par Shopify n’est sélectionnée"}}, "presets": {"name": "Vidéo"}}, "featured-product": {"name": "Produit en vedette", "blocks": {"text": {"name": "Texte", "settings": {"text": {"label": "Texte", "default": "Bloc de texte"}, "text_style": {"label": "Style", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titre"}, "price": {"name": "Prix"}, "quantity_selector": {"name": "Sélecteur de quantité"}, "variant_picker": {"name": "Sélecteur de variante", "settings": {"picker_type": {"label": "Style", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "swatch_shape": {"label": "Échantillon", "info": "En savoir plus sur [échantillons](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) sur les options de produits", "options__1": {"label": "Cercle"}, "options__2": {"label": "Carré"}, "options__3": {"label": "Aucun"}}}}, "buy_buttons": {"name": "Boutons d'achat", "settings": {"show_dynamic_checkout": {"label": "Boutons de paiement dynamique", "info": "Les clients verront leur option de paiement préférée. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Description"}, "share": {"name": "Partager", "settings": {"featured_image_info": {"content": "Si vous incluez un lien dans des publications sur les médias sociaux, l'image vedette de la page sera affichée comme image d'aperçu. [En savoir plus](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Un titre et une description de la boutique sont inclus avec l'image d'aperçu. [En savoir plus](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Texte", "default": "Partager"}}}, "rating": {"name": "Évaluation de produit", "settings": {"paragraph": {"content": "Une application est requise pour les évaluations de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous‑titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "settings": {"product": {"label": "Produit"}, "secondary_background": {"label": "Arrière-plan secondaire"}, "header": {"content": "Support multimédia"}, "enable_video_looping": {"label": "Lire la vidéo en boucle"}, "hide_variants": {"label": "Masquer les médias des variantes non sélectionnées sur le bureau"}, "media_position": {"label": "Position", "info": "La position est automatiquement optimisée pour les mobiles.", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "presets": {"name": "Produit en vedette"}}, "email-signup-banner": {"name": "Bannière d'inscription à la liste de diffusion", "settings": {"paragraph": {"content": "Ajout d’inscriptions [profils clients](https://help.shopify.com/manual/customers/manage-customers)"}, "image": {"label": "Image de fond"}, "show_background_image": {"label": "Afficher l'image de fond"}, "show_text_box": {"label": "Conteneur"}, "image_overlay_opacity": {"label": "Opacité de la superposition"}, "show_text_below": {"label": "Empiler le texte sous l’image"}, "image_height": {"label": "<PERSON><PERSON>", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}}, "desktop_content_position": {"options__4": {"label": "Au milieu à gauche"}, "options__5": {"label": "Centré au milieu"}, "options__6": {"label": "Au milieu à droite"}, "options__7": {"label": "En bas à gauche"}, "options__8": {"label": "En bas au centre"}, "options__9": {"label": "En bas à droite"}, "options__1": {"label": "En haut à gauche"}, "options__2": {"label": "En haut au centre"}, "options__3": {"label": "En haut à droite"}, "label": "Position"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement"}, "header": {"content": "Mise en page sur mobile"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement"}, "color_scheme": {"info": "Visible lorsque le conteneur est affiché."}, "content_header": {"content": "Contenu"}}, "blocks": {"heading": {"name": "<PERSON>-tête", "settings": {"heading": {"label": "<PERSON>-tête", "default": "Ouverture prochaine"}}}, "paragraph": {"name": "Texte", "settings": {"paragraph": {"label": "Texte", "default": "<p>Faites partie des premières personnes à être informées de notre lancement.</p>"}, "text_style": {"options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "label": "Style"}}}, "email_form": {"name": "Formulaire par e-mail"}}, "presets": {"name": "Bannière d'inscription à la liste de diffusion"}}, "slideshow": {"name": "Diaporama", "settings": {"layout": {"label": "Mise en page", "options__1": {"label": "<PERSON><PERSON>e largeur"}, "options__2": {"label": "Page"}}, "slide_height": {"label": "<PERSON><PERSON>", "options__1": {"label": "Adapter à la première image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}}, "slider_visual": {"label": "Pagination", "options__1": {"label": "Compteur"}, "options__2": {"label": "Points"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "auto_rotate": {"label": "Rotation automatique des diapositives"}, "change_slides_speed": {"label": "Changer de diapositive toutes les"}, "show_text_below": {"label": "Empiler le texte sous l’image"}, "mobile": {"content": "Mise en page sur mobile"}, "accessibility": {"content": "Accessibilité", "label": "Description du diaporama", "info": "Dé<PERSON><PERSON>re le diaporama pour celles et ceux qui utilisent des lecteurs d’écran", "default": "Diaporama sur votre marque"}}, "blocks": {"slide": {"name": "Diapositive", "settings": {"image": {"label": "Image"}, "heading": {"label": "<PERSON>-tête", "default": "Diapositive (image)"}, "subheading": {"label": "Sous-titre", "default": "Racontez votre histoire avec des images"}, "button_label": {"label": "Étiquette", "info": "Laisser vide pour masquer", "default": "Texte du bouton"}, "link": {"label": "<PERSON><PERSON>"}, "secondary_style": {"label": "Style de contour"}, "box_align": {"label": "Position du contenu", "options__1": {"label": "En haut à gauche"}, "options__2": {"label": "En haut au centre"}, "options__3": {"label": "En haut à droite"}, "options__4": {"label": "Centré à gauche"}, "options__5": {"label": "Centré au milieu"}, "options__6": {"label": "Au milieu à droite"}, "options__7": {"label": "En bas à gauche"}, "options__8": {"label": "En bas au centre"}, "options__9": {"label": "En bas à droite"}}, "show_text_box": {"label": "Conteneur"}, "text_alignment": {"label": "Alignement du contenu", "option_1": {"label": "G<PERSON><PERSON>"}, "option_2": {"label": "Centre"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Opacité de la superposition"}, "text_alignment_mobile": {"label": "Alignement du contenu sur mobile", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "header_button": {"content": "Bouton"}, "header_layout": {"content": "Mise en page"}, "header_text": {"content": "Texte"}, "header_colors": {"content": "Couleurs"}}}}, "presets": {"name": "Diaporama"}}, "collapsible_content": {"name": "Contenu réductible", "settings": {"caption": {"label": "Légende"}, "heading": {"label": "Titre", "default": "Contenu réductible"}, "heading_alignment": {"label": "Alignement des titres", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"label": "Conteneur", "options__1": {"label": "Aucun contenant"}, "options__2": {"label": "Conteneur de <PERSON>"}, "options__3": {"label": "Conteneur de section"}}, "container_color_scheme": {"label": "Nuancier de couleurs du conteneur"}, "open_first_collapsible_row": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> la première rangée"}, "header": {"content": "Image"}, "image": {"label": "Image"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "Grand"}}, "desktop_layout": {"label": "Placement", "options__1": {"label": "L'image en premier"}, "options__2": {"label": "L'image en deuxième"}}, "layout_header": {"content": "Mise en page"}, "section_color_scheme": {"label": "Nuancier de couleurs de la section"}}, "blocks": {"collapsible_row": {"name": "Rangée réductible", "settings": {"heading": {"label": "Titre", "default": "Rangée réductible"}, "row_content": {"label": "Contenu de la rangée"}, "page": {"label": "Contenu de la rangée de la page"}, "icon": {"label": "Icône", "options__1": {"label": "Aucun"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Bouteille"}, "options__5": {"label": "<PERSON><PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON>"}, "options__7": {"label": "Bulle de chat"}, "options__8": {"label": "Coche"}, "options__9": {"label": "Presse-papiers"}, "options__10": {"label": "Produits laitiers"}, "options__11": {"label": "Sans produits laitiers"}, "options__12": {"label": "Sèche-linge"}, "options__13": {"label": "Œil"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Sans gluten"}, "options__16": {"label": "<PERSON><PERSON><PERSON>"}, "options__17": {"label": "Fer"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON><PERSON>"}, "options__20": {"label": "Foudre"}, "options__21": {"label": "Rouge à lèvres"}, "options__22": {"label": "Cadenas"}, "options__23": {"label": "Épingle sur la carte"}, "options__24": {"label": "Sans noix"}, "options__25": {"label": "Pantalons"}, "options__26": {"label": "Empreinte"}, "options__27": {"label": "Poivre"}, "options__28": {"label": "Parfum"}, "options__29": {"label": "Avion"}, "options__30": {"label": "Plantes"}, "options__31": {"label": "Étiquette de prix"}, "options__32": {"label": "Point d'interrogation"}, "options__33": {"label": "Recyclage"}, "options__34": {"label": "Retour"}, "options__35": {"label": "<PERSON><PERSON><PERSON>"}, "options__36": {"label": "Plat de service"}, "options__37": {"label": "Chemise"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Flocon de neige"}, "options__41": {"label": "<PERSON><PERSON><PERSON>"}, "options__42": {"label": "Chronomètre"}, "options__43": {"label": "Camion"}, "options__44": {"label": "Lavage"}}}}}, "presets": {"name": "Contenu réductible"}}, "main-account": {"name": "<PERSON><PERSON><PERSON>"}, "main-activate-account": {"name": "Activation du compte"}, "main-addresses": {"name": "Adresses"}, "main-login": {"name": "Se connecter", "shop_login_button": {"enable": "Activer la connexion avec Shop"}}, "main-order": {"name": "Commande"}, "main-register": {"name": "Inscription"}, "main-reset-password": {"name": "Réinitialisation du mot de passe"}, "related-products": {"name": "Produits associés", "settings": {"heading": {"label": "<PERSON>-tête"}, "products_to_show": {"label": "Nombre de produits"}, "columns_desktop": {"label": "Colonnes"}, "paragraph__1": {"content": "Les produits associés peuvent être gérés dans l’[application Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)", "default": "Vous aimerez peut-être aussi"}, "header__2": {"content": "Carte de produit"}, "image_ratio": {"label": "Rapport d’aspect de l’image", "options__1": {"label": "Adapter à l’image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "show_vendor": {"label": "Fournisseur"}, "show_rating": {"label": "Évaluation de produit", "info": "Une application est requise pour les évaluations de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"}, "columns_mobile": {"label": "Colonnes sur mobile", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multirow": {"name": "Multiligne", "settings": {"image": {"label": "Image"}, "image_height": {"options__1": {"label": "Adapter à l’image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}, "label": "<PERSON><PERSON>"}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "label": "<PERSON><PERSON>"}, "text_style": {"options__1": {"label": "Corps"}, "options__2": {"label": "Sous‑titre"}, "label": "Style de texte"}, "button_style": {"options__1": {"label": "Bouton plein"}, "options__2": {"label": "Bouton en relief"}, "label": "Style de bouton"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement"}, "desktop_content_position": {"options__1": {"label": "En haut"}, "options__2": {"label": "Au milieu"}, "options__3": {"label": "En bas"}, "label": "Position"}, "image_layout": {"options__1": {"label": "Alterner depuis la gauche"}, "options__2": {"label": "Alterner depuis la droite"}, "options__3": {"label": "<PERSON><PERSON><PERSON> à gauche"}, "options__4": {"label": "<PERSON><PERSON><PERSON> d<PERSON>"}, "label": "Placement"}, "container_color_scheme": {"label": "Nuancier de couleurs du conteneur"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement sur mobile"}, "header": {"content": "Image"}, "header_2": {"content": "Contenu"}, "header_3": {"content": "Couleurs"}}, "blocks": {"row": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Image"}, "caption": {"label": "Légende", "default": "Légende"}, "heading": {"label": "<PERSON>-tête", "default": "<PERSON><PERSON><PERSON>"}, "text": {"label": "Texte", "default": "<p>Associez un texte à une image pour mettre en avant le produit, la collection ou l’article de blog de votre choix. Ajoutez des informations sur la disponibilité ou le style. Vous pouvez même fournir un avis.</p>"}, "button_label": {"label": "Texte du bouton", "default": "Texte du bouton", "info": "Laisser vide pour masquer"}, "button_link": {"label": "<PERSON>n du bouton"}}}}, "presets": {"name": "Multiligne"}}, "quick-order-list": {"name": "Liste rapide des commandes", "settings": {"show_image": {"label": "Images"}, "show_sku": {"label": "SKU"}, "variants_per_page": {"label": "Variantes par page"}}, "presets": {"name": "Liste rapide des commandes"}}}}