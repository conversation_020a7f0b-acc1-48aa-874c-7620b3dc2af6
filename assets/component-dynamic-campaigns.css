.dynamic-campaigns-container {
  margin: 2rem 0;
  padding: 0;
}

.campaigns-section {
  margin-bottom: 2rem;
}

.campaigns-section:last-child {
  margin-bottom: 0;
}

.campaigns-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  color: rgba(var(--color-foreground), 1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.campaign-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border-radius: 0.8rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.campaign-card:last-child {
  margin-bottom: 0;
}

.campaign-card--active {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
  border: 2px solid #28a745;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
}

.campaign-card--suggestion {
  background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
  border: 2px solid #ffc107;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.15);
}

.campaign-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.campaign-content {
  flex: 1;
  padding-right: 1rem;
}

.campaign-title {
  font-size: 1.6rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: rgba(var(--color-foreground), 1);
}

.campaign-description {
  font-size: 1.4rem;
  color: rgba(var(--color-foreground), 0.8);
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.campaign-discount {
  font-size: 1.3rem;
  font-weight: 600;
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 0.4rem;
  margin: 0.5rem 0;
  display: inline-block;
}

.campaign-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 0.8rem;
  background: rgba(var(--color-foreground), 0.1);
  border-radius: 0.4rem;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffc107 0%, #ffcd39 100%);
  border-radius: 0.4rem;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.7);
  font-weight: 500;
}

.campaign-badge {
  font-size: 2.4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.campaign-card--active .campaign-badge {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.campaign-card--suggestion .campaign-badge {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

/* Empty state */
.campaigns-empty {
  text-align: center;
  padding: 3rem 2rem;
  color: rgba(var(--color-foreground), 0.6);
}

.campaigns-empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.campaigns-empty-text {
  font-size: 1.6rem;
  margin: 0;
}

/* Mobile responsive */
@media screen and (max-width: 749px) {
  .dynamic-campaigns-container {
    margin: 1.5rem 0;
  }

  .campaign-card {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    padding: 1.2rem;
  }

  .campaign-content {
    padding-right: 0;
    margin-bottom: 1rem;
  }

  .campaign-title {
    font-size: 1.4rem;
  }

  .campaign-description {
    font-size: 1.3rem;
  }

  .campaigns-title {
    font-size: 1.6rem;
  }

  .campaign-badge {
    align-self: center;
    width: 3rem;
    height: 3rem;
    font-size: 2rem;
  }
}

/* Animation for new campaigns */
.campaign-card.campaign-new {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.campaigns-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: rgba(var(--color-foreground), 0.6);
}

.campaigns-loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid rgba(var(--color-foreground), 0.1);
  border-top: 2px solid rgba(var(--color-foreground), 0.6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Special effects for active campaigns */
.campaign-card--active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(40, 167, 69, 0.1) 50%, transparent 70%);
  animation: celebration 3s ease-in-out infinite;
  pointer-events: none;
}

@keyframes celebration {
  0%, 100% { opacity: 0; transform: translateX(-100%); }
  50% { opacity: 1; transform: translateX(100%); }
}

/* Suggestion campaigns pulse effect */
.campaign-card--suggestion .campaign-badge {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Cart drawer specific styles */
.cart-drawer-campaigns-section .dynamic-campaigns-container {
  margin: 1rem 0;
}

.cart-drawer-campaigns-section .campaign-card {
  padding: 1rem;
  margin-bottom: 0.8rem;
}

.cart-drawer-campaigns-section .campaigns-title {
  font-size: 1.4rem;
  margin-bottom: 1rem;
}

.cart-drawer-campaigns-section .campaign-title {
  font-size: 1.3rem;
}

.cart-drawer-campaigns-section .campaign-description {
  font-size: 1.2rem;
}

.cart-drawer-campaigns-section .campaign-badge {
  width: 3rem;
  height: 3rem;
  font-size: 1.8rem;
}

/* Cart page specific styles */
.cart-campaigns-section .dynamic-campaigns-container {
  margin: 2rem 0;
  padding: 2rem;
  background: rgba(var(--color-background), 1);
  border-radius: 1rem;
  border: 1px solid rgba(var(--color-foreground), 0.1);
}

.campaigns-main-title {
  font-size: 2.2rem;
  font-weight: 700;
  margin: 0 0 2rem 0;
  color: rgba(var(--color-foreground), 1);
  text-align: center;
}

/* Responsive adjustments for cart drawer */
@media screen and (max-width: 749px) {
  .cart-drawer-campaigns-section .campaign-card {
    padding: 0.8rem;
  }

  .cart-drawer-campaigns-section .campaigns-title {
    font-size: 1.3rem;
  }

  .cart-drawer-campaigns-section .campaign-title {
    font-size: 1.2rem;
  }

  .cart-drawer-campaigns-section .campaign-description {
    font-size: 1.1rem;
  }
}
