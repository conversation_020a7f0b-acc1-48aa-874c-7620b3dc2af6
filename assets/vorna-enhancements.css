/* VORNA Theme Enhancements */

/* Modern Typography */
:root {
  /* VORNA specific font sizes */
  --vorna-font-size-xs: 1.2rem;
  --vorna-font-size-sm: 1.4rem;
  --vorna-font-size-base: 1.6rem;
  --vorna-font-size-lg: 1.8rem;
  --vorna-font-size-xl: 2.4rem;
  --vorna-font-size-2xl: 3.2rem;
  --vorna-font-size-3xl: 4.8rem;
  
  /* VORNA spacing */
  --vorna-spacing-xs: 0.5rem;
  --vorna-spacing-sm: 1rem;
  --vorna-spacing-md: 2rem;
  --vorna-spacing-lg: 3rem;
  --vorna-spacing-xl: 4rem;
  --vorna-spacing-2xl: 6rem;
  
  /* VORNA transitions */
  --vorna-transition-fast: 0.15s ease;
  --vorna-transition-base: 0.3s ease;
  --vorna-transition-slow: 0.5s ease;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading-family);
  font-weight: var(--font-heading-weight);
  line-height: 1.2;
  margin: 0;
}

h1 {
  font-size: clamp(3rem, 8vw, 6rem);
  letter-spacing: -0.02em;
}

h2 {
  font-size: clamp(2.5rem, 6vw, 4rem);
  letter-spacing: -0.01em;
}

h3 {
  font-size: var(--vorna-font-size-xl);
}

p {
  font-family: var(--font-body-family);
  font-size: var(--vorna-font-size-base);
  line-height: 1.6;
  margin: 0 0 1em 0;
}

/* Enhanced Buttons */
.button {
  font-family: var(--font-body-family);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  border-radius: 0;
  transition: all var(--vorna-transition-base);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  border: 2px solid transparent;
}

.button--primary {
  background-color: rgb(var(--color-button));
  color: rgb(var(--color-button-text));
  border-color: rgb(var(--color-button));
}

.button--primary:hover {
  background-color: transparent;
  color: rgb(var(--color-button));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--color-shadow), 0.15);
}

.button--secondary {
  background-color: transparent;
  color: rgb(var(--color-foreground));
  border-color: rgb(var(--color-foreground));
}

.button--secondary:hover {
  background-color: rgb(var(--color-foreground));
  color: rgb(var(--color-background));
}

.button--large {
  padding: 1.8rem 4rem;
  font-size: var(--vorna-font-size-base);
}

.button--medium {
  padding: 1.4rem 3rem;
  font-size: var(--vorna-font-size-sm);
}

/* Enhanced Cards */
.card {
  border-radius: 1rem;
  overflow: hidden;
  transition: all var(--vorna-transition-base);
  background-color: rgb(var(--color-background));
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(var(--color-shadow), 0.1);
}

/* Enhanced Sections */
.section {
  padding: var(--vorna-spacing-2xl) 0;
}

.section--small {
  padding: var(--vorna-spacing-xl) 0;
}

.section--large {
  padding: calc(var(--vorna-spacing-2xl) * 1.5) 0;
}

/* Modern Grid System */
.vorna-grid {
  display: grid;
  gap: var(--vorna-spacing-lg);
}

.vorna-grid--2 {
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
}

.vorna-grid--3 {
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
}

.vorna-grid--4 {
  grid-template-columns: repeat(auto-fit, minmax(20rem, 1fr));
}

/* Enhanced Responsive Design */
@media screen and (max-width: 749px) {
  .vorna-grid {
    gap: var(--vorna-spacing-md);
  }
  
  .vorna-grid--2,
  .vorna-grid--3,
  .vorna-grid--4 {
    grid-template-columns: 1fr;
  }
  
  .section {
    padding: var(--vorna-spacing-xl) 0;
  }
}

/* Enhanced Focus States */
.button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid rgb(var(--color-foreground));
  outline-offset: 2px;
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease forwards;
}

/* Enhanced Text Utilities */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-heading {
  font-family: var(--font-heading-family);
  font-weight: var(--font-heading-weight);
}

.font-body {
  font-family: var(--font-body-family);
  font-weight: var(--font-body-weight);
}

/* Enhanced Spacing Utilities */
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--vorna-spacing-xs); }
.mb-2 { margin-bottom: var(--vorna-spacing-sm); }
.mb-3 { margin-bottom: var(--vorna-spacing-md); }
.mb-4 { margin-bottom: var(--vorna-spacing-lg); }
.mb-5 { margin-bottom: var(--vorna-spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--vorna-spacing-xs); }
.mt-2 { margin-top: var(--vorna-spacing-sm); }
.mt-3 { margin-top: var(--vorna-spacing-md); }
.mt-4 { margin-top: var(--vorna-spacing-lg); }
.mt-5 { margin-top: var(--vorna-spacing-xl); }
