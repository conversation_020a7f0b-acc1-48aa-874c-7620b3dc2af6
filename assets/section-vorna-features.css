.vorna-features {
  padding: 6rem 0;
}

.vorna-features__header {
  text-align: center;
  margin-bottom: 5rem;
}

.vorna-features__title {
  font-family: var(--font-heading-family);
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: var(--font-heading-weight);
  line-height: 1.2;
  color: rgb(var(--color-foreground));
  margin: 0;
}

.vorna-features__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
  gap: 4rem;
  align-items: start;
}

.vorna-features__item {
  text-align: center;
  padding: 2rem;
}

.vorna-features__image {
  margin-bottom: 2rem;
}

.vorna-features__image img {
  width: 100%;
  height: auto;
  max-width: 20rem;
  border-radius: 1rem;
}

.vorna-features__content {
  max-width: 40rem;
  margin: 0 auto;
}

.vorna-features__heading {
  font-family: var(--font-heading-family);
  font-size: 2.4rem;
  font-weight: var(--font-heading-weight);
  line-height: 1.3;
  color: rgb(var(--color-foreground));
  margin: 0 0 1.5rem 0;
}

.vorna-features__text {
  font-size: 1.6rem;
  line-height: 1.6;
  color: rgb(var(--color-foreground));
  opacity: 0.8;
}

.vorna-features__text p {
  margin: 0;
}

@media screen and (max-width: 749px) {
  .vorna-features {
    padding: 4rem 0;
  }
  
  .vorna-features__header {
    margin-bottom: 3rem;
  }
  
  .vorna-features__grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .vorna-features__item {
    padding: 1rem;
  }
  
  .vorna-features__heading {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  
  .vorna-features__text {
    font-size: 1.4rem;
  }
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .vorna-features__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (min-width: 990px) {
  .vorna-features__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
