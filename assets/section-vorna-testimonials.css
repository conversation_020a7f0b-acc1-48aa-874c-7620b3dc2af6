.vorna-testimonials {
  padding: 6rem 0;
  background-color: rgb(var(--color-background));
}

.vorna-testimonials__header {
  text-align: center;
  margin-bottom: 5rem;
}

.vorna-testimonials__title {
  font-family: var(--font-heading-family);
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: var(--font-heading-weight);
  line-height: 1.2;
  color: rgb(var(--color-foreground));
  margin: 0;
}

.vorna-testimonials__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
  gap: 3rem;
  align-items: start;
}

.vorna-testimonials__item {
  background-color: rgba(var(--color-foreground), 0.03);
  border: 1px solid rgba(var(--color-foreground), 0.1);
  border-radius: 1rem;
  padding: 3rem 2.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vorna-testimonials__item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(var(--color-shadow), 0.1);
}

.vorna-testimonials__content {
  text-align: left;
}

.vorna-testimonials__quote {
  font-size: 1.6rem;
  line-height: 1.6;
  color: rgb(var(--color-foreground));
  margin: 0 0 2rem 0;
  font-style: italic;
  position: relative;
}

.vorna-testimonials__quote::before {
  content: '"';
  font-size: 4rem;
  color: rgba(var(--color-foreground), 0.3);
  position: absolute;
  top: -1rem;
  left: -1rem;
  font-family: serif;
}

.vorna-testimonials__author {
  font-size: 1.4rem;
  font-weight: 600;
  color: rgb(var(--color-foreground));
  font-style: normal;
  display: block;
  margin-top: 1.5rem;
}

@media screen and (max-width: 749px) {
  .vorna-testimonials {
    padding: 4rem 0;
  }
  
  .vorna-testimonials__header {
    margin-bottom: 3rem;
  }
  
  .vorna-testimonials__grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .vorna-testimonials__item {
    padding: 2rem 1.5rem;
  }
  
  .vorna-testimonials__quote {
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
  }
  
  .vorna-testimonials__quote::before {
    font-size: 3rem;
    top: -0.5rem;
    left: -0.5rem;
  }
  
  .vorna-testimonials__author {
    font-size: 1.3rem;
  }
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .vorna-testimonials__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (min-width: 990px) {
  .vorna-testimonials__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
