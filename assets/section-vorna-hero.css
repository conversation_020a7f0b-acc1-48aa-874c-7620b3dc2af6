.vorna-hero {
  padding: 8rem 0;
  text-align: center;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vorna-hero__content {
  max-width: 80rem;
  margin: 0 auto;
}

.vorna-hero__heading {
  font-family: var(--font-heading-family);
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: var(--font-heading-weight);
  line-height: 1.1;
  margin-bottom: 2rem;
  color: rgb(var(--color-foreground));
}

.vorna-hero__subheading {
  font-size: 1.8rem;
  line-height: 1.4;
  margin-bottom: 3rem;
  color: rgb(var(--color-foreground));
  opacity: 0.8;
}

.vorna-hero__buttons {
  margin-top: 3rem;
}

.vorna-hero .button--large {
  padding: 1.8rem 4rem;
  font-size: 1.6rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  border-radius: 0;
  transition: all 0.3s ease;
}

.vorna-hero .button--primary {
  background-color: rgb(var(--color-button));
  color: rgb(var(--color-button-text));
  border: 2px solid rgb(var(--color-button));
}

.vorna-hero .button--primary:hover {
  background-color: transparent;
  color: rgb(var(--color-button));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--color-shadow), 0.15);
}

@media screen and (max-width: 749px) {
  .vorna-hero {
    padding: 6rem 0;
    min-height: 50vh;
  }
  
  .vorna-hero__heading {
    font-size: clamp(2.5rem, 10vw, 4rem);
    margin-bottom: 1.5rem;
  }
  
  .vorna-hero__subheading {
    font-size: 1.4rem;
    margin-bottom: 2rem;
  }
  
  .vorna-hero .button--large {
    padding: 1.5rem 3rem;
    font-size: 1.4rem;
  }
}

/* VORNA specific styling */
.vorna-hero__heading.h0 {
  font-size: clamp(4rem, 10vw, 8rem);
}

.vorna-hero__heading.h1 {
  font-size: clamp(3rem, 8vw, 6rem);
}

.vorna-hero__heading.h2 {
  font-size: clamp(2.5rem, 6vw, 4rem);
}
