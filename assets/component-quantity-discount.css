.quantity-discount-wrapper {
  margin: 2rem 0;
  padding: 2rem;
  border: 1px solid rgba(var(--color-foreground), 0.1);
  border-radius: 0.8rem;
  background: rgba(var(--color-background), 1);
}

.quantity-discount__heading {
  margin: 0 0 1.5rem 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: rgba(var(--color-foreground), 1);
}

.quantity-discount__options {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.quantity-discount__option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  border: 1px solid rgba(var(--color-foreground), 0.1);
  border-radius: 0.8rem;
  background: rgba(var(--color-background), 1);
  transition: all 0.3s ease;
  gap: 2rem;
}

.quantity-discount__option:hover {
  border-color: rgba(var(--color-foreground), 0.2);
  box-shadow: 0 2px 8px rgba(var(--color-foreground), 0.1);
  transform: translateY(-1px);
}

.quantity-discount__content {
  flex: 1;
}

.quantity-discount__label {
  font-size: 1.6rem;
  font-weight: 600;
  color: rgba(var(--color-foreground), 1);
  margin-bottom: 0.5rem;
}

.quantity-discount__percentage {
  font-size: 1.3rem;
  color: #d73527;
  font-weight: 600;
  background: rgba(215, 53, 39, 0.1);
  padding: 0.2rem 0.6rem;
  border-radius: 0.3rem;
  margin-left: 0.5rem;
}

.quantity-discount__pricing {
  display: flex;
  align-items: baseline;
  gap: 1rem;
  margin-bottom: 0.8rem;
  flex-wrap: wrap;
}

.quantity-discount__price {
  font-size: 2.4rem;
  font-weight: 700;
  color: rgba(var(--color-foreground), 1);
}

.quantity-discount__regular-price {
  font-size: 1.4rem;
  color: rgba(var(--color-foreground), 0.6);
  text-decoration: line-through;
}

.quantity-discount__savings {
  font-size: 1.3rem;
  color: #d73527;
  font-weight: 600;
  background: rgba(215, 53, 39, 0.1);
  padding: 0.2rem 0.6rem;
  border-radius: 0.3rem;
}

.quantity-discount__per-item {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.7);
}

.quantity-discount__button {
  min-width: 12rem;
  padding: 1rem 2rem;
  font-size: 1.4rem;
  font-weight: 600;
  border-radius: 0.4rem;
  transition: all 0.3s ease;
}

.quantity-discount__button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-button), 0.3);
}

.quantity-discount__button:active {
  transform: translateY(0);
}

.quantity-discount__button.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Mobile responsive */
@media screen and (max-width: 749px) {
  .quantity-discount-wrapper {
    margin: 1.5rem 0;
    padding: 1.5rem;
  }
  
  .quantity-discount__option {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1.2rem;
  }
  
  .quantity-discount__button {
    width: 100%;
    min-width: auto;
  }
  
  .quantity-discount__pricing {
    flex-wrap: wrap;
  }
  
  .quantity-discount__heading {
    font-size: 1.6rem;
  }
  
  .quantity-discount__label {
    font-size: 1.4rem;
  }

  .quantity-discount__percentage {
    font-size: 1.1rem;
    margin-left: 0.3rem;
    margin-top: 0.3rem;
    display: inline-block;
  }

  .quantity-discount__price {
    font-size: 1.8rem;
  }
}

/* Animation for success feedback */
.quantity-discount__button.success {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  color: white !important;
}

/* Loading spinner for button */
.quantity-discount__button .loading__spinner {
  width: 1.6rem;
  height: 1.6rem;
  margin-right: 0.5rem;
}
