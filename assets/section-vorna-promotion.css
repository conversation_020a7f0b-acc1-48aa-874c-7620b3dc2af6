.vorna-promotion {
  padding: 6rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.vorna-promotion::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
}

.vorna-promotion__content {
  position: relative;
  z-index: 1;
  max-width: 60rem;
  margin: 0 auto;
}

.vorna-promotion__badge {
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.2);
  color: rgb(var(--color-foreground));
  padding: 1rem 2rem;
  border-radius: 5rem;
  font-size: 1.4rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 2rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.vorna-promotion__heading {
  font-family: var(--font-heading-family);
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: var(--font-heading-weight);
  line-height: 1.2;
  color: rgb(var(--color-foreground));
  margin: 0 0 2rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.vorna-promotion__description {
  font-size: 1.8rem;
  line-height: 1.5;
  color: rgb(var(--color-foreground));
  opacity: 0.9;
  margin-bottom: 3rem;
}

.vorna-promotion__description p {
  margin: 0;
}

.vorna-promotion__button {
  margin-top: 3rem;
}

.vorna-promotion .button--large {
  padding: 2rem 4rem;
  font-size: 1.6rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.15em;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.vorna-promotion .button--large::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.vorna-promotion .button--large:hover::before {
  left: 100%;
}

.vorna-promotion .button--large:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(var(--color-shadow), 0.2);
}

/* Animated background elements */
.vorna-promotion::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@media screen and (max-width: 749px) {
  .vorna-promotion {
    padding: 4rem 0;
  }
  
  .vorna-promotion__badge {
    padding: 0.8rem 1.5rem;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
  }
  
  .vorna-promotion__heading {
    margin-bottom: 1.5rem;
  }
  
  .vorna-promotion__description {
    font-size: 1.6rem;
    margin-bottom: 2rem;
  }
  
  .vorna-promotion .button--large {
    padding: 1.5rem 3rem;
    font-size: 1.4rem;
  }
}

/* Special styling for dark color schemes */
.color-scheme-3 .vorna-promotion__badge,
.color-scheme-4 .vorna-promotion__badge {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
  color: rgb(var(--color-foreground));
}

.color-scheme-3 .vorna-promotion .button--primary,
.color-scheme-4 .vorna-promotion .button--primary {
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-button));
  border-color: rgb(var(--color-background));
}

.color-scheme-3 .vorna-promotion .button--primary:hover,
.color-scheme-4 .vorna-promotion .button--primary:hover {
  background-color: transparent;
  color: rgb(var(--color-background));
  border-color: rgb(var(--color-background));
}
