{"current": {"logo": "shopify://shop_images/527466462_17847193890537809_8438166774208222409_n.jpg", "logo_width": 90, "type_header_font": "assistant_n4", "heading_scale": 100, "type_body_font": "assistant_n4", "body_scale": 100, "page_width": 1200, "spacing_sections": 0, "spacing_grid_horizontal": 8, "spacing_grid_vertical": 8, "animations_reveal_on_scroll": true, "animations_hover_elements": "none", "buttons_border_thickness": 1, "buttons_border_opacity": 100, "buttons_radius": 0, "buttons_shadow_opacity": 0, "buttons_shadow_horizontal_offset": 0, "buttons_shadow_vertical_offset": 4, "buttons_shadow_blur": 5, "variant_pills_border_thickness": 1, "variant_pills_border_opacity": 55, "variant_pills_radius": 40, "variant_pills_shadow_opacity": 0, "variant_pills_shadow_horizontal_offset": 0, "variant_pills_shadow_vertical_offset": 4, "variant_pills_shadow_blur": 5, "inputs_border_thickness": 1, "inputs_border_opacity": 55, "inputs_radius": 0, "inputs_shadow_opacity": 0, "inputs_shadow_horizontal_offset": 0, "inputs_shadow_vertical_offset": 0, "inputs_shadow_blur": 5, "card_style": "standard", "card_image_padding": 0, "card_text_alignment": "left", "card_color_scheme": "scheme-4", "card_border_thickness": 0, "card_border_opacity": 10, "card_corner_radius": 0, "card_shadow_opacity": 0, "card_shadow_horizontal_offset": 0, "card_shadow_vertical_offset": 4, "card_shadow_blur": 5, "collection_card_style": "standard", "collection_card_image_padding": 0, "collection_card_text_alignment": "left", "collection_card_color_scheme": "scheme-2", "collection_card_border_thickness": 0, "collection_card_border_opacity": 10, "collection_card_corner_radius": 0, "collection_card_shadow_opacity": 0, "collection_card_shadow_horizontal_offset": 0, "collection_card_shadow_vertical_offset": 4, "collection_card_shadow_blur": 5, "blog_card_style": "standard", "blog_card_image_padding": 0, "blog_card_text_alignment": "left", "blog_card_color_scheme": "scheme-2", "blog_card_border_thickness": 0, "blog_card_border_opacity": 10, "blog_card_corner_radius": 0, "blog_card_shadow_opacity": 0, "blog_card_shadow_horizontal_offset": 0, "blog_card_shadow_vertical_offset": 4, "blog_card_shadow_blur": 5, "text_boxes_border_thickness": 0, "text_boxes_border_opacity": 10, "text_boxes_radius": 0, "text_boxes_shadow_opacity": 0, "text_boxes_shadow_horizontal_offset": 0, "text_boxes_shadow_vertical_offset": 4, "text_boxes_shadow_blur": 5, "media_border_thickness": 1, "media_border_opacity": 5, "media_radius": 0, "media_shadow_opacity": 0, "media_shadow_horizontal_offset": 0, "media_shadow_vertical_offset": 4, "media_shadow_blur": 5, "popup_border_thickness": 1, "popup_border_opacity": 10, "popup_corner_radius": 0, "popup_shadow_opacity": 5, "popup_shadow_horizontal_offset": 0, "popup_shadow_vertical_offset": 4, "popup_shadow_blur": 5, "drawer_border_thickness": 1, "drawer_border_opacity": 10, "drawer_shadow_opacity": 0, "drawer_shadow_horizontal_offset": 0, "drawer_shadow_vertical_offset": 4, "drawer_shadow_blur": 5, "badge_position": "bottom left", "badge_corner_radius": 40, "sale_badge_color_scheme": "scheme-4", "sold_out_badge_color_scheme": "scheme-3", "brand_headline": "GripballTR", "brand_description": "<p></p>", "brand_image_width": 100, "social_facebook_link": "", "social_instagram_link": "https://instagram.com/gripballtr", "social_youtube_link": "", "social_tiktok_link": "", "social_twitter_link": "", "social_snapchat_link": "", "social_pinterest_link": "", "social_tumblr_link": "", "social_vimeo_link": "", "predictive_search_enabled": true, "predictive_search_show_vendor": false, "predictive_search_show_price": false, "currency_code_enabled": true, "cart_type": "notification", "show_vendor": false, "show_cart_note": false, "cart_drawer_collection": "", "cart_color_scheme": "scheme-1", "dynamic_campaigns_enabled": true, "campaigns_heading": "<PERSON><PERSON><PERSON>", "cart_campaigns": "amount_threshold|500|50|500 TL Üzeri 50 TL İndirim|500 TL ve üzeri alışverişlerde 50 TL indirim\nquantity_threshold|3|15|3 Ürün Al 15 TL İndirim|3 veya daha fazla ürün alımında 15 TL indirim\nfree_shipping|750|0|750 TL Üzeri Ücretsiz Kargo|750 TL ve üzeri alışverişlerde ücretsiz kargo\npercentage_discount|1000|10|1000 TL Üzeri %10 İndirim|1000 TL ve üzeri alışverişlerde %10 indirim", "product_campaigns": "gripball-parmak-guclendirici-stres-azaltici-silikon-egzersiz-topu-ergonomik-tasarim|quantity|2|20|2'li <PERSON> - %20 İndirim|2 adet alımında %20 indirim - 240₺ tasarruf\ngripball-parmak-guclendirici-stres-azaltici-silikon-egzersiz-topu-ergonomik-tasarim|quantity|3|25|3'lü Paket - %25 İndirim|3 adet alımında %25 indirim - 450₺ tasarruf", "show_campaign_progress": true, "show_campaign_suggestions": true, "sections": {"main-password-header": {"type": "main-password-header", "settings": {"color_scheme": "scheme-1"}}, "main-password-footer": {"type": "main-password-footer", "settings": {"color_scheme": "scheme-1"}}}, "content_for_index": [], "color_schemes": {"scheme-1": {"settings": {"background": "#ffffff", "background_gradient": "", "text": "#121212", "button": "#121212", "button_label": "#ffffff", "secondary_button_label": "#121212", "shadow": "#121212"}}, "scheme-2": {"settings": {"background": "#f3f3f3", "background_gradient": "", "text": "#121212", "button": "#121212", "button_label": "#f3f3f3", "secondary_button_label": "#121212", "shadow": "#121212"}}, "scheme-3": {"settings": {"background": "#242833", "background_gradient": "", "text": "#ffffff", "button": "#ffffff", "button_label": "#000000", "secondary_button_label": "#ffffff", "shadow": "#121212"}}, "scheme-4": {"settings": {"background": "#121212", "background_gradient": "", "text": "#ffffff", "button": "#ffffff", "button_label": "#121212", "secondary_button_label": "#ffffff", "shadow": "#121212"}}, "scheme-5": {"settings": {"background": "#334fb4", "background_gradient": "", "text": "#ffffff", "button": "#ffffff", "button_label": "#334fb4", "secondary_button_label": "#ffffff", "shadow": "#121212"}}, "scheme-c295ef08-ca6d-41dc-ae21-02aa45eab2dd": {"settings": {"background": "#e72727", "background_gradient": "linear-gradient(133deg, rgba(250, 220, 54, 1) 11%, rgba(254, 98, 146, 1) 49%, rgba(247, 250, 64, 1) 61.151%, rgba(250, 220, 54, 1) 87%)", "text": "#121212", "button": "#121212", "button_label": "#ffffff", "secondary_button_label": "#121212", "shadow": "#121212"}}}}, "presets": {"Dawn": {"logo_width": 90, "color_schemes": {"scheme-1": {"settings": {"background": "#FFFFFF", "background_gradient": "", "text": "#121212", "button": "#121212", "button_label": "#FFFFFF", "secondary_button_label": "#121212", "shadow": "#121212"}}, "scheme-2": {"settings": {"background": "#F3F3F3", "background_gradient": "", "text": "#121212", "button": "#121212", "button_label": "#F3F3F3", "secondary_button_label": "#121212", "shadow": "#121212"}}, "scheme-3": {"settings": {"background": "#242833", "background_gradient": "", "text": "#FFFFFF", "button": "#FFFFFF", "button_label": "#000000", "secondary_button_label": "#FFFFFF", "shadow": "#121212"}}, "scheme-4": {"settings": {"background": "#121212", "background_gradient": "", "text": "#FFFFFF", "button": "#FFFFFF", "button_label": "#121212", "secondary_button_label": "#FFFFFF", "shadow": "#121212"}}, "scheme-5": {"settings": {"background": "#334FB4", "background_gradient": "", "text": "#FFFFFF", "button": "#FFFFFF", "button_label": "#334FB4", "secondary_button_label": "#FFFFFF", "shadow": "#121212"}}}, "type_header_font": "assistant_n4", "heading_scale": 100, "type_body_font": "assistant_n4", "body_scale": 100, "page_width": 1200, "spacing_sections": 0, "spacing_grid_horizontal": 8, "spacing_grid_vertical": 8, "animations_reveal_on_scroll": true, "animations_hover_elements": "none", "buttons_border_thickness": 1, "buttons_border_opacity": 100, "buttons_radius": 0, "buttons_shadow_opacity": 0, "buttons_shadow_horizontal_offset": 0, "buttons_shadow_vertical_offset": 4, "buttons_shadow_blur": 5, "variant_pills_border_thickness": 1, "variant_pills_border_opacity": 55, "variant_pills_radius": 40, "variant_pills_shadow_opacity": 0, "variant_pills_shadow_horizontal_offset": 0, "variant_pills_shadow_vertical_offset": 4, "variant_pills_shadow_blur": 5, "inputs_border_thickness": 1, "inputs_border_opacity": 55, "inputs_radius": 0, "inputs_shadow_opacity": 0, "inputs_shadow_horizontal_offset": 0, "inputs_shadow_vertical_offset": 4, "inputs_shadow_blur": 5, "card_style": "standard", "card_image_padding": 0, "card_text_alignment": "left", "card_color_scheme": "scheme-2", "card_border_thickness": 0, "card_border_opacity": 10, "card_corner_radius": 0, "card_shadow_opacity": 0, "card_shadow_horizontal_offset": 0, "card_shadow_vertical_offset": 4, "card_shadow_blur": 5, "collection_card_style": "standard", "collection_card_image_padding": 0, "collection_card_text_alignment": "left", "collection_card_color_scheme": "scheme-2", "collection_card_border_thickness": 0, "collection_card_border_opacity": 10, "collection_card_corner_radius": 0, "collection_card_shadow_opacity": 0, "collection_card_shadow_horizontal_offset": 0, "collection_card_shadow_vertical_offset": 4, "collection_card_shadow_blur": 5, "blog_card_style": "standard", "blog_card_image_padding": 0, "blog_card_text_alignment": "left", "blog_card_color_scheme": "scheme-2", "blog_card_border_thickness": 0, "blog_card_border_opacity": 10, "blog_card_corner_radius": 0, "blog_card_shadow_opacity": 0, "blog_card_shadow_horizontal_offset": 0, "blog_card_shadow_vertical_offset": 4, "blog_card_shadow_blur": 5, "text_boxes_border_thickness": 0, "text_boxes_border_opacity": 10, "text_boxes_radius": 0, "text_boxes_shadow_opacity": 0, "text_boxes_shadow_horizontal_offset": 0, "text_boxes_shadow_vertical_offset": 4, "text_boxes_shadow_blur": 5, "media_border_thickness": 1, "media_border_opacity": 5, "media_radius": 0, "media_shadow_opacity": 0, "media_shadow_horizontal_offset": 0, "media_shadow_vertical_offset": 4, "media_shadow_blur": 5, "popup_border_thickness": 1, "popup_border_opacity": 10, "popup_corner_radius": 0, "popup_shadow_opacity": 5, "popup_shadow_horizontal_offset": 0, "popup_shadow_vertical_offset": 4, "popup_shadow_blur": 5, "drawer_border_thickness": 1, "drawer_border_opacity": 10, "drawer_shadow_opacity": 0, "drawer_shadow_horizontal_offset": 0, "drawer_shadow_vertical_offset": 4, "drawer_shadow_blur": 5, "badge_position": "bottom left", "badge_corner_radius": 40, "sale_badge_color_scheme": "scheme-4", "sold_out_badge_color_scheme": "scheme-3", "brand_headline": "", "brand_description": "<p></p>", "brand_image_width": 100, "social_twitter_link": "", "social_facebook_link": "", "social_pinterest_link": "", "social_instagram_link": "", "social_tiktok_link": "", "social_tumblr_link": "", "social_snapchat_link": "", "social_youtube_link": "", "social_vimeo_link": "", "predictive_search_enabled": true, "predictive_search_show_vendor": false, "predictive_search_show_price": false, "currency_code_enabled": true, "cart_type": "notification", "show_vendor": false, "show_cart_note": false, "cart_drawer_collection": "", "cart_color_scheme": "scheme-1", "sections": {"main-password-header": {"type": "main-password-header", "settings": {"color_scheme": "scheme-1"}}, "main-password-footer": {"type": "main-password-footer", "settings": {"color_scheme": "scheme-1"}}}}}}