{{ 'component-dynamic-campaigns.css' | asset_url | stylesheet_tag }}

<div class="page-width section-{{ section.id }}-padding">
  <div class="page-header">
    <h1 class="main-page-title page-title h0">Kampanya Sistemi Test</h1>
  </div>

  <div class="test-section">
    <h2>🧪 Kampanya Sistemi Test Sayfası</h2>
    
    <div class="test-info">
      <p><strong>Bu sayfa kampanya sisteminin çalışıp çalışmadığını test eder.</strong></p>
      <p>Console'u açın (F12) ve hataları kontrol edin.</p>
    </div>

    <div class="test-settings">
      <h3>📋 Mevcut Ayarlar:</h3>
      <ul>
        <li><strong>Kampanyalar Etkin:</strong> {{ settings.dynamic_campaigns_enabled }}</li>
        <li><strong><PERSON><PERSON><PERSON><PERSON>:</strong> {{ settings.show_campaign_progress }}</li>
        <li><strong><PERSON>neriler Göster:</strong> {{ settings.show_campaign_suggestions }}</li>
        <li><strong>Başlık:</strong> {{ settings.campaigns_heading }}</li>
      </ul>
    </div>

    <div class="test-campaigns-raw">
      <h3>🔧 Ham Kampanya Verileri:</h3>
      <pre>{{ settings.cart_campaigns }}</pre>
    </div>

    <div class="test-cart-info">
      <h3>🛒 Mevcut Sepet:</h3>
      <p><strong>Ürün Sayısı:</strong> {{ cart.item_count }}</p>
      <p><strong>Toplam Tutar:</strong> {{ cart.total_price | money }}</p>
      <p><strong>Sepet Boş mu:</strong> {{ cart == empty }}</p>
    </div>

    <div class="test-campaigns-display">
      <h3>🎯 Kampanya Gösterimi:</h3>
      {% render 'dynamic-campaigns' %}
    </div>

    <div class="test-manual-trigger">
      <h3>🔄 Manuel Test:</h3>
      <button onclick="testCampaigns()" class="btn">Kampanyaları Yenile</button>
      <button onclick="showCartData()" class="btn">Sepet Verilerini Göster</button>
      <button onclick="showSettings()" class="btn">Ayarları Göster</button>
    </div>

    <div id="test-output" class="test-output">
      <h3>📊 Test Çıktısı:</h3>
      <pre id="test-results"></pre>
    </div>
  </div>
</div>

<style>
.test-section {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.test-info {
  background: #e3f2fd;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin: 2rem 0;
  border-left: 4px solid #2196f3;
}

.test-settings, .test-campaigns-raw, .test-cart-info {
  background: #f5f5f5;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin: 2rem 0;
}

.test-campaigns-raw pre {
  background: #fff;
  padding: 1rem;
  border-radius: 0.3rem;
  overflow-x: auto;
  font-size: 1.2rem;
}

.test-campaigns-display {
  background: #fff3e0;
  padding: 2rem;
  border-radius: 0.5rem;
  margin: 2rem 0;
  border: 2px solid #ff9800;
}

.test-manual-trigger {
  text-align: center;
  margin: 2rem 0;
}

.btn {
  background: #2196f3;
  color: white;
  border: none;
  padding: 1rem 2rem;
  margin: 0.5rem;
  border-radius: 0.3rem;
  cursor: pointer;
  font-size: 1.4rem;
}

.btn:hover {
  background: #1976d2;
}

.test-output {
  background: #f1f8e9;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin: 2rem 0;
  border-left: 4px solid #4caf50;
}

#test-results {
  background: #fff;
  padding: 1rem;
  border-radius: 0.3rem;
  min-height: 100px;
  font-size: 1.2rem;
}
</style>

<script>
function testCampaigns() {
  const results = document.getElementById('test-results');
  results.textContent = 'Testing campaigns...\n';
  
  if (window.dynamicCampaigns) {
    results.textContent += 'Campaign system found!\n';
    window.dynamicCampaigns.updateCampaigns();
    results.textContent += 'Update triggered!\n';
  } else {
    results.textContent += 'ERROR: Campaign system not found!\n';
  }
}

function showCartData() {
  const results = document.getElementById('test-results');
  results.textContent = 'Fetching cart data...\n';
  
  fetch('/cart.js')
    .then(response => response.json())
    .then(cart => {
      results.textContent = JSON.stringify(cart, null, 2);
    })
    .catch(error => {
      results.textContent = 'ERROR: ' + error.message;
    });
}

function showSettings() {
  const results = document.getElementById('test-results');
  results.textContent = 'Theme Settings:\n';
  results.textContent += JSON.stringify(window.themeSettings || {}, null, 2);
}

// Auto-run tests on page load
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    const results = document.getElementById('test-results');
    results.textContent = 'Page loaded!\n';
    results.textContent += 'Theme settings: ' + (window.themeSettings ? 'Found' : 'Not found') + '\n';
    results.textContent += 'Campaign system: ' + (window.dynamicCampaigns ? 'Found' : 'Not found') + '\n';
    results.textContent += 'Container: ' + (document.querySelector('.dynamic-campaigns-container') ? 'Found' : 'Not found') + '\n';
  }, 1000);
});
</script>
