{{ 'component-dynamic-campaigns.css' | asset_url | stylesheet_tag }}

<div class="page-width section-{{ section.id }}-padding">
  <div class="page-header">
    <h1 class="main-page-title page-title h0">
      {{ page.title | escape }}
    </h1>
  </div>

  <div class="rte">
    {{ page.content }}
  </div>

  <div class="kampanya-demo-section">
    <h2>Dinamik Kampanya Sistemi Demo</h2>
    
    <div class="demo-info">
      <p>Bu sayfa dinamik kampanya sisteminin nasıl çalıştığını gösterir. Sepetinize ürün ekleyerek kampanyaları test edebilirsiniz.</p>
    </div>

    <div class="demo-campaigns">
      <h3>Mevcut Kampanya Ayarları:</h3>
      
      <div class="demo-campaign-list">
        <div class="demo-campaign-item">
          <h4>💰 500 TL Üzeri 50 TL İndirim</h4>
          <p>Sepet tutarı 500 TL'ye ulaştığında 50 TL indirim kazanı<PERSON>ınız.</p>
        </div>
        
        <div class="demo-campaign-item">
          <h4>📦 3 Ürün Al 15 TL İndirim</h4>
          <p>Sepetinizde 3 veya daha fazla ürün olduğunda 15 TL indirim kazanırsınız.</p>
        </div>
        
        <div class="demo-campaign-item">
          <h4>🚚 750 TL Üzeri Ücretsiz Kargo</h4>
          <p>Sepet tutarı 750 TL'ye ulaştığında ücretsiz kargo kazanırsınız.</p>
        </div>
        
        <div class="demo-campaign-item">
          <h4>🎯 1000 TL Üzeri %10 İndirim</h4>
          <p>Sepet tutarı 1000 TL'ye ulaştığında %10 indirim kazanırsınız.</p>
        </div>
      </div>
    </div>

    <div class="demo-current-cart">
      <h3>Mevcut Sepet Durumu:</h3>
      <div id="cart-status">
        <div class="cart-info">
          <p><strong>Sepet Tutarı:</strong> <span id="cart-total">{{ cart.total_price | money }}</span></p>
          <p><strong>Ürün Sayısı:</strong> <span id="cart-count">{{ cart.item_count }}</span></p>
        </div>
      </div>
    </div>

    <div class="demo-campaigns-display">
      <h3>Aktif Kampanyalar:</h3>
      {% render 'dynamic-campaigns' %}
    </div>

    <div class="demo-instructions">
      <h3>Test Etmek İçin:</h3>
      <ol>
        <li>Mağazadan ürün ekleyin</li>
        <li>Sepet sayfasına gidin veya sepet drawer'ını açın</li>
        <li>Kampanyaların otomatik olarak güncellendiğini görün</li>
        <li>Kampanya koşullarını karşıladığınızda aktif kampanyalar bölümünde görün</li>
      </ol>
    </div>
  </div>
</div>

<style>
.kampanya-demo-section {
  margin: 4rem 0;
}

.demo-info {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 0.8rem;
  margin: 2rem 0;
  border-left: 4px solid #007bff;
}

.demo-campaign-list {
  display: grid;
  gap: 1.5rem;
  margin: 2rem 0;
}

.demo-campaign-item {
  background: white;
  padding: 2rem;
  border-radius: 0.8rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.demo-campaign-item h4 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.6rem;
}

.demo-campaign-item p {
  margin: 0;
  color: #6c757d;
  line-height: 1.5;
}

.demo-current-cart {
  background: #e8f5e8;
  padding: 2rem;
  border-radius: 0.8rem;
  margin: 2rem 0;
  border: 2px solid #28a745;
}

.cart-info {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.cart-info p {
  margin: 0;
  font-size: 1.4rem;
}

.demo-campaigns-display {
  margin: 3rem 0;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 0.8rem;
}

.demo-instructions {
  background: #fff3cd;
  padding: 2rem;
  border-radius: 0.8rem;
  border: 2px solid #ffc107;
  margin: 2rem 0;
}

.demo-instructions ol {
  margin: 1rem 0 0 0;
  padding-left: 2rem;
}

.demo-instructions li {
  margin: 0.5rem 0;
  line-height: 1.5;
}

@media screen and (max-width: 749px) {
  .cart-info {
    flex-direction: column;
    gap: 1rem;
  }
  
  .demo-campaign-item {
    padding: 1.5rem;
  }
  
  .demo-info, .demo-current-cart, .demo-instructions {
    padding: 1.5rem;
  }
}
</style>

<script>
// Update cart status in real-time
function updateCartStatus() {
  fetch('/cart.js')
    .then(response => response.json())
    .then(cart => {
      document.getElementById('cart-total').textContent = 
        new Intl.NumberFormat('tr-TR', {
          style: 'currency',
          currency: 'TRY'
        }).format(cart.total_price / 100);
      
      document.getElementById('cart-count').textContent = cart.item_count;
    })
    .catch(error => console.error('Error updating cart status:', error));
}

// Update every 2 seconds
setInterval(updateCartStatus, 2000);

// Update on page load
document.addEventListener('DOMContentLoaded', updateCartStatus);
</script>
