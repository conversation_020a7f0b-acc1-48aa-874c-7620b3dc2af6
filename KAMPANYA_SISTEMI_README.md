# Dinamik Kampanya Sistemi

Bu sistem, Shopify temanızda sepet bazlı dinamik kampanyalar oluşturmanıza olanak tanır. Kampanyalar gerçek zamanlı olarak hesaplanır ve kullanıcılara gösterilir.

## 🚀 Özellikler

- **Gerçek Zamanlı Kampanya Hesaplama**: Sepet değişikliklerinde otomatik güncelleme
- **Çoklu Kampanya Türleri**: Tutar, adet, ücretsiz kargo ve yüzde indirimleri
- **Görsel İlerleme Çubukları**: Kullanıcıların kampanyaya ne kadar yakın olduğunu gösterir
- **Mobil Uyumlu**: Tüm cihazlarda mükemmel görünüm
- **<PERSON>lay <PERSON>önetim**: Admin panelinden kampanya ayarları

## 📁 Dosya Yapısı

```
assets/
├── dynamic-campaigns.js          # Ana kampanya motoru
├── component-dynamic-campaigns.css  # Kampanya stilleri
└── cart.js                      # Güncellenmiş sepet dosyası

snippets/
└── dynamic-campaigns.liquid     # Kampanya gösterim snippet'i

sections/
├── main-cart-footer.liquid     # Güncellenmiş sepet footer
└── cart-drawer.liquid          # Güncellenmiş sepet drawer

templates/
└── page.kampanya-demo.liquid   # Demo sayfası

config/
├── settings_schema.json        # Tema ayarları
└── settings_data.json          # Varsayılan ayarlar
```

## ⚙️ Kurulum

### 1. Tema Ayarları

Admin panelinde **Tema Özelleştirme > Tema Ayarları > Dinamik Kampanyalar** bölümünden:

- ✅ "Dinamik kampanyaları etkinleştir" seçeneğini açın
- 📝 Kampanya başlığını ayarlayın
- 🎯 Sepet kampanyalarını tanımlayın

### 2. Kampanya Formatları

#### Sepet Kampanyaları
```
tip|koşul|değer|başlık|açıklama
```

**Desteklenen Tipler:**
- `amount_threshold`: Tutar indirimi
- `quantity_threshold`: Adet indirimi  
- `free_shipping`: Ücretsiz kargo
- `percentage_discount`: Yüzde indirimi

#### Örnek Kampanyalar
```
amount_threshold|500|50|500 TL Üzeri 50 TL İndirim|500 TL ve üzeri alışverişlerde 50 TL indirim
quantity_threshold|3|15|3 Ürün Al 15 TL İndirim|3 veya daha fazla ürün alımında 15 TL indirim
free_shipping|750|0|750 TL Üzeri Ücretsiz Kargo|750 TL ve üzeri alışverişlerde ücretsiz kargo
percentage_discount|1000|10|1000 TL Üzeri %10 İndirim|1000 TL ve üzeri alışverişlerde %10 indirim
```

## 🎨 Görünüm Özelleştirme

### CSS Sınıfları

```css
.dynamic-campaigns-container    # Ana konteyner
.campaign-card                 # Kampanya kartı
.campaign-card--active         # Aktif kampanya
.campaign-card--suggestion     # Öneri kampanyası
.campaign-progress             # İlerleme çubuğu
.campaign-discount             # İndirim gösterimi
```

### Renk Özelleştirme

```css
/* Aktif kampanya renkleri */
.campaign-card--active {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
  border-color: #28a745;
}

/* Öneri kampanya renkleri */
.campaign-card--suggestion {
  background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
  border-color: #ffc107;
}
```

## 🔧 JavaScript API

### Manuel Güncelleme
```javascript
// Kampanyaları manuel olarak güncelle
window.dynamicCampaigns.updateCampaigns();
```

### Event Dinleme
```javascript
// Kampanya güncellemelerini dinle
document.addEventListener('cart:updated', (event) => {
  console.log('Sepet güncellendi:', event.detail);
});
```

## 📱 Responsive Tasarım

Sistem otomatik olarak mobil cihazlara uyum sağlar:

- **Desktop**: Yan yana kampanya kartları
- **Tablet**: Orta boyut düzeni
- **Mobile**: Dikey yığılmış kartlar

## 🧪 Test Etme

1. **Demo Sayfası**: `/pages/kampanya-demo` sayfasını ziyaret edin
2. **Sepete Ürün Ekleyin**: Farklı miktarlarda ürün ekleyerek test edin
3. **Kampanya Kontrolü**: Sepet sayfasında kampanyaları görün

## 🔍 Sorun Giderme

### Kampanyalar Görünmüyor
- ✅ Tema ayarlarında "Dinamik kampanyalar" etkin mi?
- ✅ JavaScript dosyası yükleniyor mu?
- ✅ Kampanya formatı doğru mu?

### Kampanyalar Güncellenmiyor
- 🔄 Sayfayı yenileyin
- 🧹 Tarayıcı önbelleğini temizleyin
- 🔍 Konsol hatalarını kontrol edin

### CSS Stilleri Uygulanmıyor
- 📁 CSS dosyası yükleniyor mu?
- 🎨 Tema renk şeması uyumlu mu?
- 📱 Mobil görünümde test edin

## 🚀 Gelişmiş Özellikler

### Özel Kampanya Türleri
Yeni kampanya türleri eklemek için `dynamic-campaigns.js` dosyasını genişletin:

```javascript
case 'custom_campaign':
  // Özel kampanya mantığı
  break;
```

### Animasyonlar
Kampanya kartlarına özel animasyonlar ekleyin:

```css
.campaign-card.campaign-new {
  animation: slideInUp 0.5s ease-out;
}
```

## 📞 Destek

Herhangi bir sorun yaşarsanız:
1. Konsol hatalarını kontrol edin
2. Tema ayarlarını gözden geçirin
3. Demo sayfasını test edin

## 🔄 Güncellemeler

Sistem otomatik olarak:
- Sepet değişikliklerini algılar
- Kampanyaları yeniden hesaplar
- UI'ı günceller

---

**Not**: Bu sistem Shopify'ın mevcut indirim sistemiyle birlikte çalışır ve onu tamamlar. Gerçek indirimlerin uygulanması için Shopify admin panelinden indirim kodları oluşturmanız gerekebilir.
